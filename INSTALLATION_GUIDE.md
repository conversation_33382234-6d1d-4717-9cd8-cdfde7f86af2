# SlotGAT 环境安装指南

## 问题分析

你的原始安装脚本遇到了以下问题：

1. **PyTorch版本冲突**: `torch==1.13.1+cu117` 和 `torchvision==0.15.0+cu117` 存在依赖冲突
2. **torchaudio版本问题**: `torchaudio==0.13.1` 版本不存在或被撤回
3. **PyG依赖编译失败**: torch-sparse等包需要从源码编译，容易失败
4. **NumPy兼容性**: 新版本NumPy与旧版本PyTorch存在兼容性问题

## 解决方案

### 方案1: 修复后的pip安装 (推荐用于Colab)

使用修复后的 `setup_environment.py`:

```bash
python setup_environment.py
```

**主要修复**:
- 使用兼容的torchvision版本 (0.14.1)
- 正确的torchaudio版本 (0.13.1+cu117)
- 改进的错误处理和环境检测
- 更好的依赖安装顺序

### 方案2: Conda环境管理 (推荐用于本地开发)

使用 `setup_conda_environment.py`:

```bash
python setup_conda_environment.py
```

**优势**:
- 更好的依赖管理
- 避免版本冲突
- 独立的环境隔离
- 更稳定的CUDA支持

### 方案3: 手动安装 (最稳定)

如果自动脚本失败，可以手动安装：

```bash
# 1. 创建conda环境
conda create -n slotgat python=3.10 -y
conda activate slotgat

# 2. 安装PyTorch
conda install pytorch=1.13.1 torchvision=0.14.1 torchaudio=0.13.1 pytorch-cuda=11.7 -c pytorch -c nvidia -y

# 3. 安装基础包
conda install numpy=1.24.3 scipy=1.10.0 networkx=2.8.4 scikit-learn=1.2.1 -y

# 4. 安装DGL
pip install dgl==1.0.1+cu117 -f https://data.dgl.ai/wheels/cu117/repo.html

# 5. 安装PyG (按顺序)
pip install torch-sparse==0.6.16 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html
pip install torch-scatter==2.1.0 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html
pip install torch-cluster==1.6.0 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html
pip install torch-geometric==2.2.0
```

## 环境要求

根据项目README，需要以下环境：

- **Python**: 3.10.9 (建议3.8-3.11)
- **PyTorch**: 1.13.1
- **CUDA**: 11.7
- **DGL**: 1.0.1+cu117
- **PyTorch Geometric**: 2.2.0

## 验证安装

运行以下代码验证安装：

```python
import torch
import dgl
import torch_geometric
import torch_sparse
import torch_scatter
import torch_cluster
import networkx as nx
import scipy
import sklearn
import numpy as np

print(f"PyTorch: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"DGL: {dgl.__version__}")
print(f"PyG: {torch_geometric.__version__}")
print(f"NumPy: {np.__version__}")
```

## 常见问题

### Q1: CUDA不可用
**解决**: 检查CUDA版本，确保安装了CUDA 11.7

### Q2: PyG依赖编译失败
**解决**: 使用预编译的wheel包，确保按正确顺序安装

### Q3: 版本冲突
**解决**: 使用conda环境隔离，或者严格按照指定版本安装

### Q4: 在Colab中运行
**解决**: 使用 `setup_environment.py`，它会自动检测Colab环境

## 项目结构

安装完成后，确保在正确目录中运行：

```
SlotGAT_ICML23-new/
├── NC/methods/SlotGAT/          # 节点分类任务
│   ├── GNN_bak.py
│   ├── run_train_slotGAT_on_all_dataset.py
│   └── run_use_slotGAT_on_all_dataset.py
└── LP/methods/SlotGAT/          # 链接预测任务
    ├── GNN.py
    ├── run_train_slotGAT_on_all_dataset.py
    └── run_use_slotGAT_on_all_dataset.py
```

## 运行命令

```bash
# 节点分类
cd NC/methods/SlotGAT
python run_train_slotGAT_on_all_dataset.py  # 训练
python run_use_slotGAT_on_all_dataset.py    # 评估

# 链接预测
cd LP/methods/SlotGAT
python run_train_slotGAT_on_all_dataset.py  # 训练
python run_use_slotGAT_on_all_dataset.py    # 评估
```

## 文件说明

- `setup_environment.py`: 修复后的pip安装脚本
- `setup_conda_environment.py`: Conda环境管理脚本
- `requirements.txt`: pip依赖列表
- `INSTALLATION_GUIDE.md`: 本安装指南

选择最适合你环境的安装方法，如有问题请参考常见问题部分。
