import torch
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")

import dgl
print(f"DGL version: {dgl.__version__}")

import torch_geometric
print(f"PyG version: {torch_geometric.__version__}")

import networkx as nx
print(f"NetworkX version: {nx.__version__}")

import scipy
print(f"SciPy version: {scipy.__version__}")

import sklearn
print(f"Scikit-learn version: {sklearn.__version__}")