<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="81f46081-58ed-4341-909c-c42dfed1344e" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/LP/scripts/LP_AUC_MRR.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/LP/scripts/data_loader.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/LP/scripts/rm.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/NC/methods/SlotGAT/pipeline_utils.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/NC/methods/SlotGAT/run_train_slotGAT_on_all_dataset.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/NC/scripts/NC_F1.py" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2qk8JyqicT3HUqkQrbXv25sD5Ke" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;/home/<USER>/pycode/SlotGAT_ICML23-new/NC/methods/SlotGAT&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/NC/methods/SlotGAT" />
    </key>
  </component>
  <component name="RunManager" selected="Python.run_analysis">
    <configuration name="pipeline_utils" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="SlotGAT_ICML23-new" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/NC/methods/SlotGAT" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/NC/methods/SlotGAT/pipeline_utils.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="run_analysis" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="SlotGAT_ICML23-new" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/NC/methods/SlotGAT" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/NC/methods/SlotGAT/run_analysis.py" />
      <option name="PARAMETERS" value="--search_hidden_dim [128] --search_num_layers [2] --search_lr [0.001] --search_weight_decay [0.001] --feats-type 1 --num-heads 4 --epoch 300 --SAattDim 3 --dropout_feat 0.5 --dropout_attn 0.5 --task_property get_results --net slotGAT --slot_aggregator SA --verbose True --use_trained False --save_trained True --save_dir outputs --dataset PubMed_NC --repeat 5 --study_name get_results_dataset_PubMed_NC_net_slotGAT --cost 1 --gpu 0" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="run_train_slotGAT_on_all_dataset" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="SlotGAT_ICML23-new" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/NC/methods/SlotGAT" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/NC/methods/SlotGAT/run_train_slotGAT_on_all_dataset.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="run_use_slotGAT_on_all_dataset" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="SlotGAT_ICML23-new" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/NC/methods/SlotGAT" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/NC/methods/SlotGAT/run_use_slotGAT_on_all_dataset.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.run_analysis" />
      <item itemvalue="Python.pipeline_utils" />
      <item itemvalue="Python.run_train_slotGAT_on_all_dataset" />
      <item itemvalue="Python.run_use_slotGAT_on_all_dataset" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.run_use_slotGAT_on_all_dataset" />
        <item itemvalue="Python.run_train_slotGAT_on_all_dataset" />
        <item itemvalue="Python.pipeline_utils" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="81f46081-58ed-4341-909c-c42dfed1344e" name="Changes" comment="" />
      <created>1735195144405</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1735195144405</updated>
      <workItem from="1735195145570" duration="283000" />
      <workItem from="1735195439464" duration="27000" />
      <workItem from="1735195502614" duration="4845000" />
      <workItem from="1735264493416" duration="2201000" />
      <workItem from="1735266715100" duration="4450000" />
      <workItem from="1735278475675" duration="5522000" />
      <workItem from="1735284514352" duration="1496000" />
      <workItem from="1735288225136" duration="3830000" />
      <workItem from="1735540501207" duration="19902000" />
      <workItem from="1735602516901" duration="17124000" />
      <workItem from="1746149095404" duration="4431000" />
      <workItem from="1748399406752" duration="5148000" />
    </task>
    <servers />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$USER_HOME$/anaconda3/envs/slot/lib/python3.9/site-packages/sklearn/utils/_array_api.py</url>
          <line>178</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/NC/methods/SlotGAT/run_analysis.py</url>
          <line>110</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/NC/methods/SlotGAT/GNN_bak.py</url>
          <line>270</line>
          <option name="timeStamp" value="25" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/SlotGAT_ICML23_new$run_analysis.coverage" NAME="run_analysis Coverage Results" MODIFIED="1748409517985" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/NC/methods/SlotGAT" />
    <SUITE FILE_PATH="coverage/SlotGAT_ICML23_new$run_use_slotGAT_on_all_dataset.coverage" NAME="run_use_slotGAT_on_all_dataset Coverage Results" MODIFIED="1735566653308" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/NC/methods/SlotGAT" />
    <SUITE FILE_PATH="coverage/SlotGAT_ICML23_new$pipeline_utils.coverage" NAME="pipeline_utils Coverage Results" MODIFIED="1735195271930" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/NC/methods/SlotGAT" />
    <SUITE FILE_PATH="coverage/SlotGAT_ICML23_new$run_train_slotGAT_on_all_dataset.coverage" NAME="run_train_slotGAT_on_all_dataset Coverage Results" MODIFIED="1735546941450" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/NC/methods/SlotGAT" />
  </component>
</project>