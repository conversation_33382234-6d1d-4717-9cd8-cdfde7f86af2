import torch  # 导入PyTorch库
import torch as th  # 导入PyTorch库并重命名为th
import torch.nn.functional as F  # 导入PyTorch函数库
import torch.nn as nn  # 导入PyTorch神经网络模块
import dgl  # 导入Deep Graph Library
from dgl.nn.pytorch import GraphConv  # 导入图卷积层
import math  # 导入数学库
import dgl.function as fn  # 导入DGL函数
from dgl.nn.pytorch import edge_softmax, GATConv  # 导入边softmax和图注意力卷积
from conv import myGATConv,changedGATConv,slotGATConv  # 导入自定义的卷积层
from torch.profiler import profile, record_function, ProfilerActivity  # 导入PyTorch性能分析工具
from torch_geometric.typing import OptPairTensor, OptTensor, Size, Tensor  # 导入PyTorch Geometric类型
from typing import Callable, Tuple, Union  # 导入类型提示
from torch_scatter import scatter_add  # 导入散点加法函数
from torch_geometric.utils import add_remaining_self_loops  # 导入添加自环的工具
from dgl._ffi.base import DGLError  # 导入DGL错误类
from typing import List, NamedTuple, Optional, Tuple, Union  # 导入更多类型提示



from torch.nn import Linear  # 导入线性层
from torch_geometric.nn.conv import MessagePassing, GCNConv  # 导入消息传递和GCN卷积层

class Adj(NamedTuple):  # 定义邻接矩阵类
    edge_index: torch.Tensor  # 边索引
    edge_features: torch.Tensor  # 边特征
    size: Tuple[int, int]  # 大小
    target_size: int  # 目标大小

    def to(self, *args, **kwargs):  # 转换设备方法
        return Adj(
            self.edge_index.to(*args, **kwargs),
            self.edge_features.to(*args, **kwargs),
            self.size,
            self.target_size
        )
    




class MLP(nn.Module):  # 多层感知机类
    def __init__(self,
                 g,  # 图
                 in_dims,  # 输入维度
                 num_hidden,  # 隐藏层维度
                 num_classes,  # 类别数
                 num_layers,  # 层数
                 activation,  # 激活函数
                 dropout):  # 丢弃率
        super(MLP, self).__init__()
        self.num_classes=num_classes  # 保存类别数
        self.layers = nn.ModuleList()  # 创建层列表
        self.fc_list = nn.ModuleList([nn.Linear(in_dim, num_hidden, bias=True) for in_dim in in_dims])  # 为每种节点类型创建输入层
        for fc in self.fc_list:  # 初始化输入层权重
            nn.init.xavier_normal_(fc.weight, gain=1.414)
        # 输入层
        self.layers.append(nn.Linear(num_hidden, num_hidden, bias=True))
        # 隐藏层
        for i in range(num_layers - 1):
            self.layers.append(nn.Linear(num_hidden, num_hidden))
        # 输出层
        self.layers.append(nn.Linear(num_hidden, num_classes))
        for ly in self.layers:  # 初始化层权重
            nn.init.xavier_normal_(ly.weight, gain=1.414)
        self.dropout = nn.Dropout(p=dropout)  # 丢弃层

    def forward(self, features_list, e_feat):  # 前向传播
        h = []
        for fc, feature in zip(self.fc_list, features_list):  # 对每种节点类型的特征进行转换
            h.append(fc(feature))
        h = torch.cat(h, 0)  # 拼接所有节点特征

        for i, layer in enumerate(self.layers):  # 通过每一层
            encoded_embeddings=h  # 保存编码嵌入
            h = self.dropout(h)  # 应用丢弃
            h = layer(h)  # 通过层
            h=F.relu(h) if i<len(self.layers) else h  # 如果不是最后一层，应用ReLU激活

        return h,encoded_embeddings  # 返回最终输出和编码嵌入



class LabelPropagation(nn.Module):  # 标签传播类
    r"""
    描述
    -----------
    在《Learning from Labeled and Unlabeled Data with Label Propagation》中引入
    .. math::
        \mathbf{Y}^{\prime} = \alpha \cdot \mathbf{D}^{-1/2} \mathbf{A}
        \mathbf{D}^{-1/2} \mathbf{Y} + (1 - \alpha) \mathbf{Y},
    其中未标记数据通过标记数据的传播来推断。
    参数
    ----------
        num_layers: int
            传播的次数。
        alpha: float
            \alpha 系数。
    """
    def __init__(self, num_layers, alpha):
        super(LabelPropagation, self).__init__()

        self.num_layers = num_layers  # 传播层数
        self.alpha = alpha  # alpha系数
    
    @torch.no_grad()  # 不计算梯度
    def forward(self, g, labels, mask,get_out="False"):    # 标签形状=(类型0的节点数) 可能包含错误标签，因此这里的mask参数提供训练节点的索引非常重要
        with g.local_scope():  # 在本地范围内操作
            if labels.dtype == torch.long:  # 如果标签是长整型
                labels = F.one_hot(labels.view(-1)).to(torch.float32)  # 转换为one-hot向量
            y=torch.zeros((g.num_nodes(),labels.shape[1])).to(labels.device)  # 创建全零标签矩阵
            y[mask] = labels[mask]  # 为训练节点设置标签
            
            last = (1 - self.alpha) * y  # 保存上一次迭代结果的部分
            degs = g.in_degrees().float().clamp(min=1)  # 获取入度
            norm = torch.pow(degs, -0.5).to(labels.device).unsqueeze(1)  # 计算归一化系数

            for _ in range(self.num_layers):  # 进行多次传播
                # 假设图是无向的
                g.ndata['h'] = y * norm  # 设置节点特征
                g.update_all(fn.copy_u('h', 'm'), fn.sum('m', 'h'))  # 聚合邻居信息
                y = last + self.alpha * g.ndata.pop('h') * norm  # 更新标签
                y=F.normalize(y,p=1,dim=1)   # 按行用p-1范数归一化y
                y[mask] = labels[mask]  # 保持训练节点的标签不变
                last = (1 - self.alpha) * y  # 更新上一次迭代结果
            
            return y,None  # 返回最终标签





       
class slotGAT(nn.Module):  # 槽位图注意力网络类
    def __init__(self,
                 g,  # 图
                 edge_dim,  # 边维度
                 num_etypes,  # 边类型数
                 in_dims,  # 输入维度
                 num_hidden,  # 隐藏层维度
                 num_classes,  # 类别数
                 num_layers,  # 层数
                 heads,  # 注意力头数
                 activation,  # 激活函数
                 feat_drop,  # 特征丢弃率
                 attn_drop,  # 注意力丢弃率
                 negative_slope,  # LeakyReLU负斜率
                 residual,  # 是否使用残差连接
                 alpha,  # alpha系数
                 num_ntype,  # 节点类型数
                 eindexer, aggregator="SA",predicted_by_slot="None", addLogitsTrain="None",  SAattDim=32,dataRecorder=None,targetTypeAttention="False",vis_data_saver=None):
        super(slotGAT, self).__init__()
        self.g = g  # 保存图
        self.num_layers = num_layers  # 层数
        self.gat_layers = nn.ModuleList()  # GAT层列表
        self.activation = activation  # 激活函数
        self.fc_list = nn.ModuleList([nn.Linear(in_dim, num_hidden, bias=True) for in_dim in in_dims])  # 为每种节点类型创建输入层
        self.num_ntype=num_ntype  # 节点类型数
        self.num_classes=num_classes  # 类别数
        self.leaky_relu = nn.LeakyReLU(negative_slope)  # LeakyReLU激活
        self.predicted_by_slot=predicted_by_slot  # 由槽位预测
        self.addLogitsTrain=addLogitsTrain  # 添加逻辑训练
        self.SAattDim=SAattDim  # 槽位注意力维度
        self.vis_data_saver=vis_data_saver  # 可视化数据保存器
        self.dataRecorder=dataRecorder  # 数据记录器
        
        if aggregator=="SA":  # 如果使用槽位注意力聚合器
            last_dim=num_classes  # 最后一层维度
                
            self.macroLinear=nn.Linear(last_dim, self.SAattDim, bias=True);nn.init.xavier_normal_(self.macroLinear.weight, gain=1.414);nn.init.normal_(self.macroLinear.bias, std=1.414*math.sqrt(1/(self.macroLinear.bias.flatten().shape[0])))  # 宏观线性层
            self.macroSemanticVec=nn.Parameter(torch.FloatTensor(self.SAattDim,1));nn.init.normal_(self.macroSemanticVec,std=1)  # 宏观语义向量
            
 
 
        self.last_fc = nn.Parameter(th.FloatTensor(size=(num_classes*self.num_ntype, num_classes))) ;nn.init.xavier_normal_(self.last_fc, gain=1.414)  # 最后的全连接层
        for fc in self.fc_list:  # 初始化输入层权重
            nn.init.xavier_normal_(fc.weight, gain=1.414)
        # 输入投影（无残差）
        self.gat_layers.append(slotGATConv(edge_dim, num_etypes,
            num_hidden, num_hidden, heads[0],
            feat_drop, attn_drop, negative_slope, False, self.activation, alpha=alpha,num_ntype=num_ntype, eindexer=eindexer,inputhead=True, dataRecorder=dataRecorder))
        # 隐藏层
        for l in range(1, num_layers):
            # 由于多头注意力，输入维度 = num_hidden * num_heads
            self.gat_layers.append(slotGATConv(edge_dim, num_etypes,
                num_hidden* heads[l-1] , num_hidden, heads[l],
                feat_drop, attn_drop, negative_slope, residual, self.activation, alpha=alpha,num_ntype=num_ntype, eindexer=eindexer, dataRecorder=dataRecorder))
        # 输出投影
        self.gat_layers.append(slotGATConv(edge_dim, num_etypes,
            num_hidden* heads[-2] , num_classes, heads[-1],
            feat_drop, attn_drop, negative_slope, residual, None, alpha=alpha,num_ntype=num_ntype, eindexer=eindexer, dataRecorder=dataRecorder))
        self.aggregator=aggregator  # 聚合器
        self.by_slot=[f"by_slot_{nt}" for nt in range(g.num_ntypes)]  # 按槽位列表
        assert aggregator in (["onedimconv","average","last_fc","max","SA"]+self.by_slot)  # 断言聚合器在可用选项中
        if self.aggregator=="onedimconv":  # 如果使用一维卷积聚合器
            self.nt_aggr=nn.Parameter(torch.FloatTensor(1,1,self.num_ntype,1));nn.init.normal_(self.nt_aggr,std=1)  # 节点类型聚合权重
        self.epsilon = torch.FloatTensor([1e-12]).cuda()  # 小常数，避免除零
    def l2byslot(self,x):  # 按槽位进行L2归一化
        
        x=x.view(-1, self.num_ntype,int(x.shape[1]/self.num_ntype))  # 重塑为(节点数, 节点类型数, 特征维度)
        x=x / (torch.max(torch.norm(x, dim=2, keepdim=True), self.epsilon))  # L2归一化
        x=x.flatten(1)  # 展平为(节点数, 节点类型数*特征维度)
        return x

    def forward(self, features_list,e_feat, get_out="False"):  # 前向传播函数
        with record_function("model_forward"):  # 使用性能分析器记录前向传播
            encoded_embeddings=None  # 初始化编码嵌入
            h = []  # 初始化特征列表
            for nt_id,(fc, feature) in enumerate(zip(self.fc_list, features_list)):  # 对每种节点类型的特征进行处理
                nt_ft=fc(feature)  # 通过线性层转换特征
                emsen_ft=torch.zeros([nt_ft.shape[0],nt_ft.shape[1]*self.num_ntype]).to(feature.device)  # 创建扩展特征空间
                emsen_ft[:,nt_ft.shape[1]*nt_id:nt_ft.shape[1]*(nt_id+1)]=nt_ft  # 将特征放入对应的槽位
                h.append(emsen_ft)   # 添加到特征列表，节点类型ID决定了特征位置
            h = torch.cat(h, 0)        # 拼接所有节点特征，形状为 num_nodes*(num_type*hidden_dim)
            res_attn = None  # 初始化残差注意力
            for l in range(self.num_layers):  # 通过每个隐藏层
                h, res_attn = self.gat_layers[l](self.g, h, e_feat,get_out=get_out, res_attn=res_attn)   # 通过槽位GAT层，形状为 num_nodes*num_heads*(num_ntype*hidden_dim)
                h = h.flatten(1)    # 展平为 num_nodes*(num_heads*num_ntype*hidden_dim)
                encoded_embeddings=h  # 保存编码嵌入
            # 输出投影
            logits, _ = self.gat_layers[-1](self.g, h, e_feat,get_out=get_out, res_attn=None)   # 通过最后一层，形状为 num_nodes*num_heads*num_ntype*hidden_dim
        
        if self.aggregator=="SA" :  # 如果使用槽位注意力聚合器
            logits=logits.squeeze(1)  # 去除单维度
            logits=self.l2byslot(logits)  # 按槽位进行L2归一化
            logits=logits.view(-1, self.num_ntype,int(logits.shape[1]/self.num_ntype))  # 重塑为(节点数, 节点类型数, 特征维度)
            
            if "getSlots" in get_out:  # 如果需要获取槽位信息
                self.logits=logits.detach()  # 保存分离的logits

             
            
            slot_scores=(F.tanh(self.macroLinear(logits))@self.macroSemanticVec).mean(0,keepdim=True)  # 计算槽位得分，形状为 num_slots
            self.slot_scores=F.softmax(slot_scores,dim=1)  # softmax归一化得分
            logits=(logits*self.slot_scores).sum(1)  # 加权求和得到最终logits
            if  self.dataRecorder["meta"]["getSAAttentionScore"]=="True":  # 如果需要记录槽位注意力得分
                self.dataRecorder["data"][f"{self.dataRecorder['status']}_SAAttentionScore"]=self.slot_scores.flatten().tolist() # 记录分布统计

        # 在节点类型上平均
        if self.predicted_by_slot!="None" and self.training==False:  # 如果使用特定槽位预测且在测试阶段
            with record_function("predict_by_slot"):  # 性能分析
                logits=logits.view(-1,1,self.num_ntype,self.num_classes)  # 重塑为(节点数,1,节点类型数,类别数)
                if self.predicted_by_slot=="max":  # 如果使用最大槽位
                    if "getMaxSlot" in  get_out:  # 如果需要获取最大槽位
                        maxSlotIndexesWithLabels=logits.max(2)[1].squeeze(1)  # 获取每个节点每个类别的最大槽位索引
                        logits_indexer=logits.max(2)[0].max(2)[1]  # 获取每个节点的最大类别索引
                        self.maxSlotIndexes=torch.gather(maxSlotIndexesWithLabels,1,logits_indexer)  # 获取最大槽位索引
                    logits=logits.max(2)[0]  # 在槽位维度上取最大值
                elif self.predicted_by_slot=="all":  # 如果使用所有槽位
                    if "getSlots" in get_out:  # 如果需要获取槽位信息
                        self.logits=logits.detach()  # 保存分离的logits
                    logits=logits.view(-1,1,self.num_ntype,self.num_classes).mean(2)  # 在槽位维度上平均

                else:  # 如果使用特定槽位
                    target_slot=int(self.predicted_by_slot)  # 目标槽位
                    logits=logits[:,:,target_slot,:].squeeze(2)  # 选择目标槽位
        else:  # 如果不使用特定槽位预测或在训练阶段
            # 使用槽位聚合器
            if self.aggregator=="average":  # 如果使用平均聚合器
                logits=logits.view(-1,1,self.num_ntype,self.num_classes).mean(2)  # 在槽位维度上平均
            elif self.aggregator=="onedimconv":  # 如果使用一维卷积聚合器
                logits=(logits.view(-1,1,self.num_ntype,self.num_classes)*F.softmax(self.leaky_relu(self.nt_aggr),dim=2)).sum(2)  # 加权求和
            elif self.aggregator=="last_fc":  # 如果使用最后全连接层聚合器
                logits=logits.view(-1,1,self.num_ntype,self.num_classes)  # 重塑
                logits=logits.flatten(1)  # 展平
                logits=logits.matmul(self.last_fc).unsqueeze(1)  # 通过全连接层
            elif self.aggregator=="max":  # 如果使用最大值聚合器
                logits=logits.view(-1,1,self.num_ntype,self.num_classes).max(2)[0]  # 在槽位维度上取最大值
        
            elif self.aggregator=="None":  # 如果不使用聚合器
            
                logits=logits.view(-1,1, self.num_ntype,self.num_classes).flatten(2)  # 展平为(节点数,1,节点类型数*类别数)
            elif  self.aggregator== "SA":  # 如果使用槽位注意力聚合器
                logits=logits.view(-1,1, 1,self.num_classes).flatten(2)  # 展平为(节点数,1,类别数)



            else:
                raise NotImplementedError()  # 未实现的聚合器
        # 在注意力头上平均
        ### logits = [节点数 * 注意力头数 * 类别数]
        self.logits_mean=logits.flatten().mean()  # 计算logits平均值
        logits = logits.mean(1)  # 在注意力头维度上平均
        
        # 这是tf.l2_normalize的等效替代，参见 https://www.tensorflow.org/versions/r1.15/api_docs/python/tf/math/l2_normalize 了解更多信息。
        logits = logits / (torch.max(torch.norm(logits, dim=1, keepdim=True), self.epsilon))  # L2归一化
        return logits, encoded_embeddings    # 返回logits和编码嵌入






  
class changedGAT(nn.Module):  # 修改版图注意力网络类
    def __init__(self,
                 g,  # 图
                 edge_dim,  # 边维度
                 num_etypes,  # 边类型数
                 in_dims,  # 输入维度
                 num_hidden,  # 隐藏层维度
                 num_classes,  # 类别数
                 num_layers,  # 层数
                 heads,  # 注意力头数
                 activation,  # 激活函数
                 feat_drop,  # 特征丢弃率
                 attn_drop,  # 注意力丢弃率
                 negative_slope,  # LeakyReLU负斜率
                 residual,  # 是否使用残差连接
                 alpha,  # alpha系数
                 num_ntype,  # 节点类型数
                 eindexer, ):
        super(changedGAT, self).__init__()
        self.g = g  # 保存图
        self.num_layers = num_layers  # 层数
        self.gat_layers = nn.ModuleList()  # GAT层列表
        self.activation = activation  # 激活函数
        self.fc_list = nn.ModuleList([nn.Linear(in_dim, num_hidden, bias=True) for in_dim in in_dims])  # 为每种节点类型创建输入层
        #self.ae_drop=nn.Dropout(feat_drop)
        #if ae_layer=="last_hidden":
            #self.lc_ae=nn.ModuleList([nn.Linear(num_hidden * heads[-2],num_hidden, bias=True),nn.Linear(num_hidden,num_ntype, bias=True)])
        for fc in self.fc_list:  # 初始化输入层权重
            nn.init.xavier_normal_(fc.weight, gain=1.414)
        # 输入投影（无残差）
        self.gat_layers.append(changedGATConv(edge_dim, num_etypes,
            num_hidden, num_hidden, heads[0],
            feat_drop, attn_drop, negative_slope, False, self.activation, alpha=alpha,num_ntype=num_ntype, eindexer=eindexer))
        # 隐藏层
        for l in range(1, num_layers):
            # 由于多头注意力，输入维度 = num_hidden * num_heads
            self.gat_layers.append(changedGATConv(edge_dim, num_etypes,
                num_hidden * heads[l-1], num_hidden, heads[l],
                feat_drop, attn_drop, negative_slope, residual, self.activation, alpha=alpha,num_ntype=num_ntype, eindexer=eindexer))
        # 输出投影
        self.gat_layers.append(changedGATConv(edge_dim, num_etypes,
            num_hidden * heads[-2], num_classes, heads[-1],
            feat_drop, attn_drop, negative_slope, residual, None, alpha=alpha,num_ntype=num_ntype,  eindexer=eindexer))
        self.epsilon = torch.FloatTensor([1e-12]).cuda()  # 小常数，避免除零

    def forward(self, features_list, e_feat,get_out="False"):  # 前向传播函数

        hidden_logits=None  # 初始化隐藏logits
        h = []  # 初始化特征列表
        for fc, feature in zip(self.fc_list, features_list):  # 对每种节点类型的特征进行处理
            h.append(fc(feature))   # 通过线性层转换特征，节点类型ID决定了特征位置
        h = torch.cat(h, 0)  # 拼接所有节点特征
        res_attn = None  # 初始化残差注意力
        for l in range(self.num_layers):  # 通过每个隐藏层
            h, res_attn = self.gat_layers[l](self.g, h, e_feat, res_attn=res_attn)  # 通过改变的GAT层
            h = h.flatten(1)  # 展平特征
            #if self.ae_layer=="last_hidden":
            encoded_embeddings=h  # 保存编码嵌入
            """for i in range(len(self.lc_ae)):
                _h=self.lc_ae[i](_h)
                if i==0:
                    _h=self.ae_drop(_h)
                    _h=F.relu(_h)
            hidden_logits=_h"""
        # 输出投影
        logits, _ = self.gat_layers[-1](self.g, h, e_feat, res_attn=None)  # 通过最后一层
        logits = logits.mean(1)  # 在注意力头维度上平均
        # 这是tf.l2_normalize的等效替代，参见 https://www.tensorflow.org/versions/r1.15/api_docs/python/tf/math/l2_normalize 了解更多信息。
        logits = logits / (torch.max(torch.norm(logits, dim=1, keepdim=True), self.epsilon))  # L2归一化
        return logits, encoded_embeddings    #hidden_logits






class myGAT(nn.Module):  # 自定义图注意力网络类
    def __init__(self,
                 g,  # 图
                 edge_dim,  # 边维度
                 num_etypes,  # 边类型数
                 in_dims,  # 输入维度
                 num_hidden,  # 隐藏层维度
                 num_classes,  # 类别数
                 num_layers,  # 层数
                 heads,  # 注意力头数
                 activation,  # 激活函数
                 feat_drop,  # 特征丢弃率
                 attn_drop,  # 注意力丢弃率
                 negative_slope,  # LeakyReLU负斜率
                 residual,  # 是否使用残差连接
                 alpha, dataRecorder=None):  # alpha系数和数据记录器
        super(myGAT, self).__init__()
        self.g = g  # 保存图
        self.num_layers = num_layers  # 层数
        self.gat_layers = nn.ModuleList()  # GAT层列表
        self.activation = activation  # 激活函数
        self.fc_list = nn.ModuleList([nn.Linear(in_dim, num_hidden, bias=True) for in_dim in in_dims])  # 为每种节点类型创建输入层
        for fc in self.fc_list:  # 初始化输入层权重
            nn.init.xavier_normal_(fc.weight, gain=1.414)
        # 输入投影（无残差）
        self.gat_layers.append(myGATConv(edge_dim, num_etypes,
            num_hidden, num_hidden, heads[0],
            feat_drop, attn_drop, negative_slope, False, self.activation, alpha=alpha))
        # 隐藏层
        for l in range(1, num_layers):
            # 由于多头注意力，输入维度 = num_hidden * num_heads
            self.gat_layers.append(myGATConv(edge_dim, num_etypes,
                num_hidden * heads[l-1], num_hidden, heads[l],
                feat_drop, attn_drop, negative_slope, residual, self.activation, alpha=alpha))
        # 输出投影
        self.gat_layers.append(myGATConv(edge_dim, num_etypes,
            num_hidden * heads[-2], num_classes, heads[-1],
            feat_drop, attn_drop, negative_slope, residual, None, alpha=alpha))
        self.epsilon = torch.FloatTensor([1e-12]).cuda()  # 小常数，避免除零
        self.dataRecorder=dataRecorder  # 数据记录器

    def forward(self, features_list, e_feat, get_out="False"):  # 前向传播函数


        h = []  # 初始化特征列表
        for fc, feature in zip(self.fc_list, features_list):  # 对每种节点类型的特征进行处理
            h.append(fc(feature))   # 通过线性层转换特征，节点类型ID决定了特征位置
        h = torch.cat(h, 0)  # 拼接所有节点特征
        res_attn = None  # 初始化残差注意力
        for l in range(self.num_layers):  # 通过每个隐藏层
            h, res_attn = self.gat_layers[l](self.g, h, e_feat, res_attn=res_attn)  # 通过自定义GAT层
            h = h.flatten(1)  # 展平特征
            encoded_embeddings=h  # 保存编码嵌入
        # 输出投影
        logits, _ = self.gat_layers[-1](self.g, h, e_feat, res_attn=None)  # 通过最后一层
        logits = logits.mean(1)  # 在注意力头维度上平均
        # 这是tf.l2_normalize的等效替代，参见 https://www.tensorflow.org/versions/r1.15/api_docs/python/tf/math/l2_normalize 了解更多信息。
        logits = logits / (torch.max(torch.norm(logits, dim=1, keepdim=True), self.epsilon))  # L2归一化
        return logits,encoded_embeddings  # 返回logits和编码嵌入

class RGAT(nn.Module):  # 关系图注意力网络类
    def __init__(self,
                 gs,  # 图列表
                 in_dims,  # 输入维度
                 num_hidden,  # 隐藏层维度
                 num_classes,  # 类别数
                 num_layers,  # 层数
                 heads,  # 注意力头数
                 activation,  # 激活函数
                 feat_drop,  # 特征丢弃率
                 attn_drop,  # 注意力丢弃率
                 negative_slope,  # LeakyReLU负斜率
                 residual ):  # 是否使用残差连接
        super(GAT, self).__init__()
        self.gs = gs  # 保存图列表
        self.num_layers = num_layers  # 层数
        self.gat_layers = nn.ModuleList([nn.ModuleList() for i in range(len(gs))])  # 为每个图创建GAT层列表
        self.activation = activation  # 激活函数
        self.weights = nn.Parameter(torch.zeros((len(in_dims), num_layers+1, len(gs))))  # 权重参数
        self.sm = nn.Softmax(2)  # Softmax层
        self.fc_list = nn.ModuleList([nn.Linear(in_dim, num_hidden, bias=True) for in_dim in in_dims])  # 为每种节点类型创建输入层
        for fc in self.fc_list:  # 初始化输入层权重
            nn.init.xavier_normal_(fc.weight, gain=1.414)
        for i in range(len(gs)):  # 为每个图创建GAT层
            # 输入投影（无残差）
            self.gat_layers[i].append(GATConv(
                num_hidden, num_hidden, heads[0],
                feat_drop, attn_drop, negative_slope, False, self.activation))
            # 隐藏层
            for l in range(1, num_layers):
                # 由于多头注意力，输入维度 = num_hidden * num_heads
                self.gat_layers[i].append(GATConv(
                    num_hidden * heads[l-1], num_hidden, heads[l],
                    feat_drop, attn_drop, negative_slope, residual, self.activation))
            # 输出投影
            self.gat_layers[i].append(GATConv(
                num_hidden * heads[-2], num_classes, heads[-1],
                feat_drop, attn_drop, negative_slope, residual, None))

    def forward(self, features_list):  # 前向传播函数
        nums = [feat.size(0) for feat in features_list]  # 每种节点类型的节点数
        weights = self.sm(self.weights)  # 计算权重
        h = []  # 初始化特征列表
        for fc, feature in zip(self.fc_list, features_list):  # 对每种节点类型的特征进行处理
            h.append(fc(feature))  # 通过线性层转换特征
        h = torch.cat(h, 0)  # 拼接所有节点特征
        for l in range(self.num_layers):  # 通过每个隐藏层
            out = []  # 初始化输出列表
            for i in range(len(self.gs)):  # 对每个图
                out.append(torch.split(self.gat_layers[i][l](self.gs[i], h).flatten(1), nums))  # 通过GAT层并按节点类型分割
            h = []  # 重置特征列表
            for k in range(len(nums)):  # 对每种节点类型
                tmp = []  # 临时列表
                for i in range(len(self.gs)):  # 对每个图
                    tmp.append(out[i][k]*weights[k,l,i])  # 加权
                h.append(sum(tmp))  # 求和
            h = torch.cat(h, 0)  # 拼接所有节点特征
        out = []  # 初始化输出列表
        for i in range(len(self.gs)):  # 对每个图
            out.append(torch.split(self.gat_layers[i][-1](self.gs[i], h).mean(1), nums))  # 通过最后一层并按节点类型分割
        logits = []  # 初始化logits列表
        for k in range(len(nums)):  # 对每种节点类型
            tmp = []  # 临时列表
            for i in range(len(self.gs)):  # 对每个图
                tmp.append(out[i][k]*weights[k,-1,i])  # 加权
            logits.append(sum(tmp))  # 求和
        logits = torch.cat(logits, 0)  # 拼接所有节点的logits
        return logits  # 返回logits

class GAT(nn.Module):  # 标准图注意力网络类
    def __init__(self,
                 g,  # 图
                 in_dims,  # 输入维度
                 num_hidden,  # 隐藏层维度
                 num_classes,  # 类别数
                 num_layers,  # 层数
                 heads,  # 注意力头数
                 activation,  # 激活函数
                 feat_drop,  # 特征丢弃率
                 attn_drop,  # 注意力丢弃率
                 negative_slope,  # LeakyReLU负斜率
                 residual,dataRecorder=None ):  # 是否使用残差连接和数据记录器
        super(GAT, self).__init__()
        self.g = g  # 保存图
        self.num_layers = num_layers  # 层数
        self.gat_layers = nn.ModuleList()  # GAT层列表
        self.activation = activation  # 激活函数
        self.fc_list = nn.ModuleList([nn.Linear(in_dim, num_hidden, bias=True) for in_dim in in_dims])  # 为每种节点类型创建输入层
        self.dataRecorder=dataRecorder  # 数据记录器
        for fc in self.fc_list:  # 初始化输入层权重
            nn.init.xavier_normal_(fc.weight, gain=1.414)
        # 输入投影（无残差）
        self.gat_layers.append(GATConv(
            num_hidden, num_hidden, heads[0],
            feat_drop, attn_drop, negative_slope, False, self.activation))
        # 隐藏层
        for l in range(1, num_layers):
            # 由于多头注意力，输入维度 = num_hidden * num_heads
            self.gat_layers.append(GATConv(
                num_hidden * heads[l-1], num_hidden, heads[l],
                feat_drop, attn_drop, negative_slope, residual, self.activation))
        # 输出投影
        self.gat_layers.append(GATConv(
            num_hidden * heads[-2], num_classes, heads[-1],
            feat_drop, attn_drop, negative_slope, residual, None))

    def forward(self, features_list, e_feat,get_out="False"):  # 前向传播函数
        h = []  # 初始化特征列表
        for fc, feature in zip(self.fc_list, features_list):  # 对每种节点类型的特征进行处理
            h.append(fc(feature))  # 通过线性层转换特征
        h = torch.cat(h, 0)  # 拼接所有节点特征
        for l in range(self.num_layers):  # 通过每个隐藏层
            h = self.gat_layers[l](self.g, h).flatten(1)  # 通过GAT层并展平特征
            encoded_embeddings=h  # 保存编码嵌入
        # 输出投影
        logits = self.gat_layers[-1](self.g, h).mean(1)  # 通过最后一层并在注意力头维度上平均
        return logits,encoded_embeddings  # 返回logits和编码嵌入


class GCN(nn.Module):  # 图卷积网络类
    def __init__(self,
                 g,  # 图
                 in_dims,  # 输入维度
                 num_hidden,  # 隐藏层维度
                 num_classes,  # 类别数
                 num_layers,  # 层数
                 activation,  # 激活函数
                 dropout, dataRecorder=None):  # 丢弃率和数据记录器
        super(GCN, self).__init__()
        self.g = g  # 保存图
        self.layers = nn.ModuleList()  # 层列表
        self.fc_list = nn.ModuleList([nn.Linear(in_dim, num_hidden, bias=True) for in_dim in in_dims])  # 为每种节点类型创建输入层
        self.dataRecorder=dataRecorder  # 数据记录器
        for fc in self.fc_list:  # 初始化输入层权重
            nn.init.xavier_normal_(fc.weight, gain=1.414)
        # 输入层
        self.layers.append(GraphConv(num_hidden, num_hidden, activation=activation, weight=False))
        # 隐藏层
        for i in range(num_layers - 1):
            self.layers.append(GraphConv(num_hidden, num_hidden, activation=activation))
        # 输出层
        self.layers.append(GraphConv(num_hidden, num_classes))
        self.dropout = nn.Dropout(p=dropout)  # 丢弃层

    def forward(self, features_list, e_feat,get_out="False"):  # 前向传播函数
        h = []  # 初始化特征列表
        for fc, feature in zip(self.fc_list, features_list):  # 对每种节点类型的特征进行处理
            h.append(fc(feature))  # 通过线性层转换特征
        h = torch.cat(h, 0)  # 拼接所有节点特征
        for i, layer in enumerate(self.layers):  # 通过每一层
            encoded_embeddings=h  # 保存编码嵌入
            h = self.dropout(h)  # 应用丢弃
            h = layer(self.g, h)  # 通过图卷积层
        return h,encoded_embeddings  # 返回最终输出和编码嵌入




def gcn_norm(edge_index, edge_weight=None, num_nodes=None, improved=False,
             add_self_loops=True, dtype=None,norm="D^{-1/2}(A+I)D^{-1/2}",attn_drop=None):  # 图卷积归一化函数

    fill_value = 2. if improved else 1.  # 填充值，改进版本为2，否则为1
    num_nodes = int(edge_index.max()) + 1 if num_nodes is None else num_nodes  # 节点数
    if edge_weight is None:  # 如果没有提供边权重
        edge_weight = torch.ones((edge_index.size(1), ), dtype=dtype,
                                 device=edge_index.device)  # 创建全1边权重

    if add_self_loops:  # 如果添加自环
        edge_index, tmp_edge_weight = add_remaining_self_loops(
            edge_index, edge_weight, fill_value, num_nodes)  # 添加剩余自环
        assert tmp_edge_weight is not None
        edge_weight = tmp_edge_weight
        
    row, col = edge_index[0], edge_index[1]  # 行列索引
    deg = scatter_add(edge_weight, col, dim=0, dim_size=num_nodes)  # 计算度
    if norm=="D^{-1/2}(A+I)D^{-1/2}":  # 标准对称归一化
        deg_inv_sqrt = deg.pow_(-0.5)  # 度的-0.5次方
        deg_inv_sqrt.masked_fill_(deg_inv_sqrt == float('inf'), 0)  # 掩盖无穷大值
        return edge_index, attn_drop(deg_inv_sqrt[row] * edge_weight * deg_inv_sqrt[col])  # 返回归一化后的边
    elif norm=="D^{-1}(A+I)":  # 非对称归一化
        deg_inv_sqrt = deg.pow_(-1)  # 度的-1次方
        deg_inv_sqrt.masked_fill_(deg_inv_sqrt == float('inf'), 0)  # 掩盖无穷大值
        return edge_index, attn_drop(deg_inv_sqrt[row] * edge_weight )  # 返回归一化后的边
    elif norm=="(A+I)D^{-1}":  # 非对称归一化（反向）
        deg_inv_sqrt = deg.pow_(-1)  # 度的-1次方
        deg_inv_sqrt.masked_fill_(deg_inv_sqrt == float('inf'), 0)  # 掩盖无穷大值
        return edge_index, attn_drop(deg_inv_sqrt[col] * edge_weight )  # 返回归一化后的边
    elif norm=="(A+I)":  # 无归一化
        return edge_index, attn_drop(edge_weight )  # 返回原始边
    else:
        raise Exception(f"No specified norm: {norm}")  # 未指定的归一化方法




