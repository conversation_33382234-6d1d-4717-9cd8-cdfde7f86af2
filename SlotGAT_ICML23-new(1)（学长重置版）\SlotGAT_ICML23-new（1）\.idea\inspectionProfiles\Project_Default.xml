<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="14">
            <item index="0" class="java.lang.String" itemvalue="scipy" />
            <item index="1" class="java.lang.String" itemvalue="torchmetrics" />
            <item index="2" class="java.lang.String" itemvalue="networkx" />
            <item index="3" class="java.lang.String" itemvalue="ema_pytorch" />
            <item index="4" class="java.lang.String" itemvalue="json5" />
            <item index="5" class="java.lang.String" itemvalue="scikit_learn" />
            <item index="6" class="java.lang.String" itemvalue="dgl" />
            <item index="7" class="java.lang.String" itemvalue="torch" />
            <item index="8" class="java.lang.String" itemvalue="numpy" />
            <item index="9" class="java.lang.String" itemvalue="ogb" />
            <item index="10" class="java.lang.String" itemvalue="pandas" />
            <item index="11" class="java.lang.String" itemvalue="tqdm" />
            <item index="12" class="java.lang.String" itemvalue="wandb" />
            <item index="13" class="java.lang.String" itemvalue="torch_geometric" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>