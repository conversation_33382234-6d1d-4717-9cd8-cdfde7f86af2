#!/usr/bin/env python3
"""
SlotGAT Google Colab 快速设置脚本
一键配置所有依赖和环境
"""

import os
import sys

def main():
    print("🚀 SlotGAT Colab 快速设置开始...")
    
    # 检查Colab环境
    try:
        import google.colab
        print("✅ 检测到Google Colab环境")
    except ImportError:
        print("❌ 请在Google Colab中运行此脚本")
        return
    
    # 显示系统信息
    print(f"🐍 Python版本: {sys.version}")
    
    # 安装命令列表
    commands = [
        # 清理环境
        "pip uninstall -y torch torchvision torchaudio dgl torch-geometric torch-sparse torch-scatter torch-cluster",
        
        # 安装核心依赖
        "pip install numpy==1.24.3",
        
        # 安装PyTorch生态
        "pip install torch==1.13.1+cu117 torchvision==0.14.1+cu117 torchaudio==0.13.1+cu117 --extra-index-url https://download.pytorch.org/whl/cu117",
        
        # 安装DGL
        "pip install dgl==1.0.1+cu117 -f https://data.dgl.ai/wheels/cu117/repo.html",
        
        # 安装PyG依赖
        "pip install torch-sparse==0.6.16 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html",
        "pip install torch-scatter==2.1.0 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html",
        "pip install torch-cluster==1.6.0 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html",
        
        # 安装PyG主包
        "pip install torch-geometric==2.2.0",
        
        # 安装其他依赖
        "pip install networkx==2.8.4 scikit-learn==1.2.1 scipy==1.10.0"
    ]
    
    print("\n📦 开始安装依赖包...")
    for i, cmd in enumerate(commands, 1):
        print(f"\n[{i}/{len(commands)}] 执行: {cmd}")
        result = os.system(cmd)
        if result == 0:
            print("✅ 成功")
        else:
            print("⚠️ 可能有警告，继续...")
    
    print("\n🎉 依赖安装完成!")
    print("\n⚠️ 重要提示:")
    print("1. 请重启Colab运行时 (Runtime -> Restart Runtime)")
    print("2. 重启后运行验证代码确认安装成功")
    print("3. 下载项目数据到Google Drive")
    
    # 生成验证代码
    verification_code = '''
# 验证安装 - 重启运行时后运行
import torch
import dgl
import torch_geometric
print(f"PyTorch: {torch.__version__}")
print(f"DGL: {dgl.__version__}")  
print(f"PyG: {torch_geometric.__version__}")
print(f"CUDA: {torch.cuda.is_available()}")
'''
    
    print("\n📋 验证代码 (重启后运行):")
    print(verification_code)

if __name__ == "__main__":
    main()
