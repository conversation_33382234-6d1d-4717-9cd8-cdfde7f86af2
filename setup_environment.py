# ==================== SlotGAT 环境配置脚本 (兼容性修复版) ====================
print("🚀 开始配置 SlotGAT 环境...")

import os
import sys
import subprocess

def run_command(cmd):
    """运行命令并显示输出"""
    print(f"执行: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"❌ 错误: {result.stderr}")
        return False
    else:
        print("✅ 成功")
        return True

def check_python_version():
    """检查Python版本兼容性"""
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")

    if version.major != 3:
        print("❌ 需要Python 3.x版本")
        return False

    if version.minor < 8:
        print("❌ Python版本过低，建议使用Python 3.8+")
        return False

    if version.minor >= 12:
        print("⚠️ Python 3.12+可能存在兼容性问题，建议使用Python 3.8-3.11")

    return True

if not check_python_version():
    print("请使用合适的Python版本")
    sys.exit(1)

# 1. 清理环境
print("\n🧹 清理现有环境...")
run_command("pip uninstall -y torch torchvision torchaudio dgl torch-geometric torch-sparse torch-scatter torch-cluster")

# 2. 安装兼容的NumPy版本 (先安装，避免后续冲突)
print("\n📦 安装NumPy...")
run_command("pip install numpy==1.24.3")

# 3. 安装PyTorch生态系统 (按正确顺序)
print("\n🔥 安装PyTorch 1.13.1...")
# 使用兼容的torchvision版本
run_command("pip install torch==1.13.1+cu117 torchvision==0.14.1+cu117 --extra-index-url https://download.pytorch.org/whl/cu117")

# 安装兼容的torchaudio
print("安装torchaudio...")
run_command("pip install torchaudio==0.13.1+cu117 --extra-index-url https://download.pytorch.org/whl/cu117")

# 4. 安装DGL
print("\n🌐 安装DGL...")
run_command("pip install dgl==1.0.1+cu117 -f https://data.dgl.ai/wheels/cu117/repo.html")

# 5. 安装PyTorch Geometric及其依赖
print("\n📊 安装PyTorch Geometric...")
# 先安装PyG依赖包
print("安装PyG依赖包...")
run_command("pip install torch-sparse==0.6.16 torch-scatter==2.1.0 torch-cluster==1.6.0 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html")

# 再安装PyG主包
print("安装PyTorch Geometric主包...")
run_command("pip install torch-geometric==2.2.0")

# 6. 安装其他科学计算依赖
print("\n🔬 安装其他依赖...")
run_command("pip install networkx==2.8.4 scikit-learn==1.2.1 scipy==1.10.0")

# 7. 验证安装
print("\n🔍 验证安装...")

def verify_package(package_name, import_name=None):
    """验证单个包的安装"""
    if import_name is None:
        import_name = package_name

    try:
        module = __import__(import_name)
        version = getattr(module, '__version__', 'Unknown')
        print(f"  ✅ {package_name}: {version}")
        return True
    except ImportError as e:
        print(f"  ❌ {package_name}: 导入失败 - {e}")
        return False

# 验证核心依赖
success_count = 0
total_packages = 0

packages_to_verify = [
    ("NumPy", "numpy"),
    ("PyTorch", "torch"),
    ("DGL", "dgl"),
    ("PyTorch Geometric", "torch_geometric"),
    ("torch-sparse", "torch_sparse"),
    ("torch-scatter", "torch_scatter"),
    ("torch-cluster", "torch_cluster"),
    ("NetworkX", "networkx"),
    ("SciPy", "scipy"),
    ("Scikit-learn", "sklearn")
]

for package_name, import_name in packages_to_verify:
    total_packages += 1
    if verify_package(package_name, import_name):
        success_count += 1

print(f"\n📊 安装结果: {success_count}/{total_packages} 包成功安装")

if success_count == total_packages:
    print("🎉 所有依赖安装成功!")

    # 额外验证CUDA支持
    try:
        import torch
        if torch.cuda.is_available():
            print(f"🚀 CUDA可用: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️ CUDA不可用，将使用CPU模式")
    except:
        pass

elif success_count >= 7:  # 至少核心包安装成功
    print("⚠️ 部分依赖安装成功，可以尝试运行")
else:
    print("❌ 关键依赖安装失败，请检查错误信息")
    print("💡 建议:")
    print("  1. 重启Python环境")
    print("  2. 检查CUDA版本是否为11.7")
    print("  3. 尝试使用conda环境")

# 8. 环境检测和目录设置
print("\n🌍 检测运行环境...")

# 检测是否在Colab环境
def is_colab():
    try:
        import google.colab
        return True
    except ImportError:
        return False

def setup_directories():
    """设置项目目录"""
    if is_colab():
        print("� 检测到Google Colab环境")
        try:
            from google.colab import drive
            drive.mount('/content/drive')
            print("✅ Google Drive 挂载成功")

            # Colab环境的项目路径
            project_path = '/content/drive/MyDrive/SlotGAT_Project/SlotGAT_ICML23-new(1)（学长重置版）/SlotGAT_ICML23-new（1）/NC/methods/SlotGAT'

            if os.path.exists(project_path):
                os.chdir(project_path)
                print(f"✅ 切换到项目目录: {os.getcwd()}")
                return True
            else:
                print(f"❌ 项目目录不存在: {project_path}")
                return False

        except Exception as e:
            print(f"❌ Drive挂载失败: {e}")
            return False
    else:
        print("💻 检测到本地环境")
        # 本地环境，使用当前目录
        current_dir = os.getcwd()
        print(f"✅ 当前工作目录: {current_dir}")

        # 检查是否在SlotGAT项目目录中
        expected_files = ['GNN_bak.py', 'run_train_slotGAT_on_all_dataset.py']
        if any(os.path.exists(f) for f in expected_files):
            print("✅ 检测到SlotGAT项目文件")
            return True
        else:
            print("⚠️ 未检测到SlotGAT项目文件，请确保在正确目录中运行")
            return True  # 继续执行，让用户手动检查

# 设置目录
setup_success = setup_directories()

# 9. 检查关键文件
print("\n📁 检查项目文件...")
key_files = ['GNN_bak.py', 'run_analysis.py', 'run_train_slotGAT_on_all_dataset.py']
for file in key_files:
    if os.path.exists(file):
        print(f"  ✅ {file}")
    else:
        print(f"  ❌ {file} 不存在")

# 10. 创建必要目录
print("\n📂 创建必要目录...")
dirs = ['outputs', 'log', 'checkpoint', 'analysis']
for dir_name in dirs:
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
        print(f"  ✅ 创建 {dir_name}/")
    else:
        print(f"  📁 {dir_name}/ 已存在")

# 11. 最终状态报告
print("\n" + "="*50)
print("🎉 环境配置完成!")
print("="*50)

if success_count == total_packages:
    print("✅ 所有依赖安装成功")
elif success_count >= 7:
    print("⚠️ 部分依赖安装成功，可以尝试运行")
else:
    print("❌ 依赖安装存在问题，请检查上述错误")

print("\n🚀 接下来可以运行:")
if is_colab():
    print("  训练: !python run_train_slotGAT_on_all_dataset.py")
    print("  评估: !python run_use_slotGAT_on_all_dataset.py")
else:
    print("  训练: python run_train_slotGAT_on_all_dataset.py")
    print("  评估: python run_use_slotGAT_on_all_dataset.py")

print("\n💡 如果遇到问题:")
print("  1. 重启Python环境后重新运行此脚本")
print("  2. 检查CUDA版本是否为11.7")
print("  3. 确保在正确的项目目录中")
print("  4. 考虑使用conda环境管理依赖")

if __name__ == "__main__":
    pass





