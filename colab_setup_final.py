# ==================== SlotGAT Colab 最终安装脚本 ====================
import os
import sys
import subprocess

def run_cmd(cmd):
    print(f"执行: {cmd}")
    result = os.system(cmd)
    if result == 0:
        print("✅ 成功")
        return True
    else:
        print("❌ 失败")
        return False

print("🚀 SlotGAT Colab环境配置开始...")
print(f"Python版本: {sys.version}")

# 1. 重启前清理
print("\n🧹 清理环境...")
run_cmd("pip uninstall -y torch torchvision torchaudio dgl torch-geometric torch-sparse torch-scatter torch-cluster")

# 2. 安装NumPy (固定版本)
print("\n📦 安装NumPy...")
run_cmd("pip install numpy==1.24.3")

# 3. 安装PyTorch (使用兼容版本)
print("\n🔥 安装PyTorch...")
run_cmd("pip install torch==1.13.1+cu117 torchvision==0.14.1+cu117 torchaudio==0.13.1+cu117 --extra-index-url https://download.pytorch.org/whl/cu117")

# 4. 安装DGL
print("\n🌐 安装DGL...")
run_cmd("pip install dgl==1.0.1+cu117 -f https://data.dgl.ai/wheels/cu117/repo.html")

# 5. 安装PyG依赖 (分步安装)
print("\n📊 安装PyG依赖...")
run_cmd("pip install torch-sparse==0.6.16 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html")
run_cmd("pip install torch-scatter==2.1.0 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html") 
run_cmd("pip install torch-cluster==1.6.0 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html")

# 6. 安装PyG主包
print("\n📈 安装PyTorch Geometric...")
run_cmd("pip install torch-geometric==2.2.0")

# 7. 安装其他依赖
print("\n🔬 安装其他依赖...")
run_cmd("pip install networkx==2.8.4 scikit-learn==1.2.1 scipy==1.10.0")

print("\n✅ 安装完成！请重启运行时后验证安装")
print("重启后运行以下代码验证:")
print("""
import torch
import dgl  
import torch_geometric
print(f"PyTorch: {torch.__version__}")
print(f"CUDA: {torch.cuda.is_available()}")
print(f"DGL: {dgl.__version__}")
print(f"PyG: {torch_geometric.__version__}")
""")
