# Transformer超参数实验指南

## 1. 实验目标

你在GNN_bak.py中添加了Transformer模块，现在需要通过实验找到最佳的参数配置，提升模型性能。

## 2. 当前代码位置

### 第228行 - Transformer模块定义
```python
self.transformer_encoder= nn.TransformerEncoder(nn.TransformerEncoderLayer(d_model=512, nhead=8).to(device="cuda:0"), num_layers=2).to(device="cuda:0")
```

### 第250-253行 - 残差连接部分
```python
h1 = self.transformer_encoder(h.to(device="cuda:0"))
#是否残差
h=h1
#h=h1+h
```

## 3. 需要调优的参数

### 3.1 Transformer参数（第228行）

| 参数名 | 当前值 | 建议实验范围 | 说明 |
|--------|--------|-------------|------|
| d_model | 512 | [256, 512, 768, 1024] | 模型维度，影响表达能力 |
| nhead | 8 | [4, 8, 12, 16] | 注意力头数，必须能整除d_model |
| num_layers | 2 | [1, 2, 3, 4, 6] | Transformer层数 |
| dropout | 0.1(默认) | [0.0, 0.1, 0.2, 0.3] | 防止过拟合 |

### 3.2 残差连接策略（第250-253行）

| 策略 | 代码写法 | 说明 |
|------|----------|------|
| 无残差 | `h = h1` | 当前使用的方式 |
| 标准残差 | `h = h + h1` | 把注释去掉 |
| 加权残差 | `h = 0.3*h + 0.7*h1` | 可以调整权重比例 |

## 4. 实验计划（分3个阶段）

### 阶段1：基线对比实验
**目的**：验证残差连接是否有效

| 实验编号 | d_model | nhead | num_layers | 残差策略 | 备注 |
|----------|---------|-------|------------|----------|------|
| Exp_001 | 512 | 8 | 2 | 无残差 | 当前配置 |
| Exp_002 | 512 | 8 | 2 | 标准残差 | 去掉注释 |

**操作步骤**：
1. 先跑Exp_001（当前配置）记录结果
2. 修改第253行：把`#h=h1+h`的注释去掉，注释掉`h=h1`
3. 跑Exp_002，对比结果

### 阶段2：Transformer参数调优
**目的**：找到最佳的Transformer配置

选择阶段1中表现更好的残差策略，然后调优Transformer参数：

#### 2.1 模型维度实验
| 实验编号 | d_model | nhead | num_layers | 说明 |
|----------|---------|-------|------------|------|
| Exp_003 | 256 | 8 | 2 | 小模型，训练快 |
| Exp_004 | 512 | 8 | 2 | 基线配置 |
| Exp_005 | 768 | 12 | 2 | 大模型，表达能力强 |

#### 2.2 注意力头数实验
| 实验编号 | d_model | nhead | num_layers | 说明 |
|----------|---------|-------|------------|------|
| Exp_006 | 512 | 4 | 2 | 较少注意力头 |
| Exp_007 | 512 | 8 | 2 | 基线配置 |
| Exp_008 | 512 | 16 | 2 | 较多注意力头 |

#### 2.3 层数实验
| 实验编号 | d_model | nhead | num_layers | 说明 |
|----------|---------|-------|------------|------|
| Exp_009 | 512 | 8 | 1 | 浅层网络 |
| Exp_010 | 512 | 8 | 2 | 基线配置 |
| Exp_011 | 512 | 8 | 3 | 中等深度 |
| Exp_012 | 512 | 8 | 4 | 较深网络 |

### 阶段3：精细调优
**目的**：在最佳配置基础上进一步优化

使用阶段2找到的最佳Transformer配置，尝试不同的残差权重：

| 实验编号 | 残差策略 | 代码写法 | 说明 |
|----------|----------|----------|------|
| Exp_013 | 偏向原特征 | `h = 0.7*h + 0.3*h1` | 保留更多原始信息 |
| Exp_014 | 平衡 | `h = 0.5*h + 0.5*h1` | 平衡组合 |
| Exp_015 | 偏向新特征 | `h = 0.3*h + 0.7*h1` | 更依赖Transformer输出 |

## 5. 如何修改代码

### 5.1 修改Transformer参数（第228行）
```python
# 原来的代码
self.transformer_encoder= nn.TransformerEncoder(nn.TransformerEncoderLayer(d_model=512, nhead=8).to(device="cuda:0"), num_layers=2).to(device="cuda:0")

# 修改示例（比如改成d_model=256, nhead=4, num_layers=3）
self.transformer_encoder= nn.TransformerEncoder(nn.TransformerEncoderLayer(d_model=256, nhead=4).to(device="cuda:0"), num_layers=3).to(device="cuda:0")
```

### 5.2 修改残差连接（第250-253行）
```python
# 当前代码
h1 = self.transformer_encoder(h.to(device="cuda:0"))
#是否残差
h=h1
#h=h1+h

# 改成标准残差
h1 = self.transformer_encoder(h.to(device="cuda:0"))
#是否残差
#h=h1
h=h+h1

# 改成加权残差（比如0.3*原特征 + 0.7*新特征）
h1 = self.transformer_encoder(h.to(device="cuda:0"))
#是否残差
#h=h1
h=0.3*h+0.7*h1
```

## 6. 实验记录表格

建议创建一个Excel表格记录实验结果：

| 实验编号 | d_model | nhead | num_layers | 残差策略 | 准确率 | F1分数 | 训练时间 | 备注 |
|----------|---------|-------|------------|----------|--------|--------|----------|------|
| Exp_001 | 512 | 8 | 2 | 无残差 | | | | 基线 |
| Exp_002 | 512 | 8 | 2 | 标准残差 | | | | |
| ... | ... | ... | ... | ... | ... | ... | ... | ... |

## 7. 实验建议

### 7.1 实验顺序
1. **先做阶段1**：确定残差连接是否有用
2. **再做阶段2**：一次只改一个参数，看哪个影响最大
3. **最后做阶段3**：在最佳配置基础上精细调优

### 7.2 注意事项
- **一次只改一个参数**：这样能清楚知道哪个参数的影响
- **记录详细信息**：每次实验都要记录配置和结果
- **注意内存限制**：d_model太大可能显存不够
- **确保可重现**：设置随机种子，保证结果可重复

### 7.3 快速验证技巧
- 可以先用小数据集快速测试
- 训练几个epoch就能看出趋势
- 如果某个配置明显很差，可以提前停止

## 8. 预期结果

- **残差连接**：通常会提升性能，特别是层数较多时
- **d_model**：适中的值（512）通常效果不错
- **nhead**：8是比较好的起点
- **num_layers**：2-4层通常足够，太深可能过拟合

## 9. 总结

这个实验计划帮你系统地找到最佳的Transformer配置。记住：
1. 一步一步来，不要一次改太多参数
2. 详细记录每次实验的结果
3. 先验证残差连接的效果
4. 然后逐个调优Transformer参数
5. 最后精细调整残差权重

通过这样的实验，你应该能找到比当前配置更好的参数组合！
