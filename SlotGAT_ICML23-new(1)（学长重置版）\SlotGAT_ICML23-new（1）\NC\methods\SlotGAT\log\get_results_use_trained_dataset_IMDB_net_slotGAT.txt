../../data/IMDB
2024-12-27 17:02:49
model using: slotGAT
222222222
1111111
[[0 0 0 0 1]
 [0 0 0 0 1]
 [0 1 0 0 0]
 ...
 [0 0 0 0 1]
 [1 0 0 0 1]
 [0 1 0 0 1]]
1111111
[[0 0 0 1 0]
 [0 0 0 1 0]
 [0 1 0 1 0]
 ...
 [0 0 1 0 1]
 [0 1 0 0 1]
 [1 0 1 0 1]]
{'micro-f1': 0.6690626517727052, 'macro-f1': 0.5906676376912373, 'mcm': array([[[2436,   33],
        [ 662,   71]],

       [[1867,  382],
        [ 286,  667]],

       [[1629,  277],
        [ 446,  850]],

       [[2194,  200],
        [ 338,  470]],

       [[1033,  422],
        [ 361, 1386]]])}
model using: slotGAT
222222222
1111111
[[0 0 1 0 1]
 [0 0 1 0 0]
 [0 0 1 0 0]
 ...
 [1 0 0 1 0]
 [1 0 0 0 1]
 [0 1 0 0 1]]
1111111
[[0 0 0 1 0]
 [0 0 0 1 0]
 [0 1 0 1 0]
 ...
 [0 0 1 0 1]
 [0 1 0 0 1]
 [1 0 1 0 1]]
{'micro-f1': 0.6902654867256638, 'macro-f1': 0.6493976922543082, 'mcm': array([[[2324,  145],
        [ 494,  239]],

       [[1902,  347],
        [ 304,  649]],

       [[1651,  255],
        [ 422,  874]],

       [[2218,  176],
        [ 333,  475]],

       [[1033,  422],
        [ 357, 1390]]])}
model using: slotGAT
222222222
1111111
[[0 0 0 0 1]
 [1 0 1 0 0]
 [0 0 0 0 1]
 ...
 [0 0 0 0 1]
 [0 1 0 1 0]
 [0 0 1 0 1]]
1111111
[[0 0 0 1 0]
 [0 0 0 1 0]
 [0 1 0 1 0]
 ...
 [0 0 1 0 1]
 [0 1 0 0 1]
 [1 0 1 0 1]]
{'micro-f1': 0.6843988780346262, 'macro-f1': 0.6373718792350854, 'mcm': array([[[2348,  121],
        [ 531,  202]],

       [[1894,  355],
        [ 304,  649]],

       [[1671,  235],
        [ 429,  867]],

       [[2240,  154],
        [ 356,  452]],

       [[1056,  399],
        [ 379, 1368]]])}
model using: slotGAT
222222222
1111111
[[0 1 0 0 1]
 [0 0 0 0 1]
 [0 1 0 1 0]
 ...
 [1 1 0 1 0]
 [0 0 1 0 0]
 [0 0 0 1 1]]
1111111
[[0 0 0 1 0]
 [0 0 0 1 0]
 [0 1 0 1 0]
 ...
 [0 0 1 0 1]
 [0 1 0 0 1]
 [1 0 1 0 1]]
{'micro-f1': 0.6805821524320183, 'macro-f1': 0.6317146422052635, 'mcm': array([[[2350,  119],
        [ 552,  181]],

       [[1883,  366],
        [ 289,  664]],

       [[1662,  244],
        [ 441,  855]],

       [[2200,  194],
        [ 323,  485]],

       [[1025,  430],
        [ 378, 1369]]])}
model using: slotGAT
222222222
1111111
[[0 0 0 0 1]
 [0 0 0 0 1]
 [0 0 0 0 1]
 ...
 [0 0 1 1 0]
 [0 0 0 1 1]
 [0 1 0 0 1]]
1111111
[[0 0 0 1 0]
 [0 0 0 1 0]
 [0 1 0 1 0]
 ...
 [0 0 1 0 1]
 [0 1 0 0 1]
 [1 0 1 0 1]]
{'micro-f1': 0.6866297921037573, 'macro-f1': 0.6427458926448925, 'mcm': array([[[2343,  126],
        [ 529,  204]],

       [[1938,  311],
        [ 323,  630]],

       [[1663,  243],
        [ 442,  854]],

       [[2195,  199],
        [ 291,  517]],

       [[ 985,  470],
        [ 352, 1395]]])}
mean and std of macro-f1: 63.0±2.1
mean and std of micro-f1: 68.2±0.7
