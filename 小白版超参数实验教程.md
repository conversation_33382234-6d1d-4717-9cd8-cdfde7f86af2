# 小白版超参数实验教程

## 什么是超参数？为什么要调？

### 简单比喻
想象你在做菜：
- **食材** = 你的数据
- **菜谱** = 你的模型代码
- **调料用量** = 超参数（比如盐放多少、糖放多少）

现在你的"菜"（模型）味道还可以，但你想让它更好吃。怎么办？调整调料用量！

### 你的情况
你在代码里加了一个Transformer模块（就像加了一种新调料），但现在调料用量是随便设的：
- 盐放了512克（d_model=512）
- 糖放了8勺（nhead=8）  
- 胡椒粉撒了2次（num_layers=2）

现在要通过实验找到最佳用量，让"菜"更好吃（模型性能更好）。

## 你需要调整的"调料"在哪里？

### 位置1：第228行（Transformer的调料用量）
```python
self.transformer_encoder= nn.TransformerEncoder(nn.TransformerEncoderLayer(d_model=512, nhead=8).to(device="cuda:0"), num_layers=2).to(device="cuda:0")
```

这行代码里有3个数字需要调：
- **d_model=512**：模型大小（像主菜分量）
- **nhead=8**：注意力头数（像配菜种类）
- **num_layers=2**：层数（像烹饪步骤数）

### 位置2：第250-253行（要不要混合调料）
```python
h1 = self.transformer_encoder(h.to(device="cuda:0"))
#是否残差
h=h1          # 只用新调料
#h=h1+h       # 新调料+原调料混合（这行被注释了）
```

这里决定：是只用新调料，还是新旧调料混合？

## 实验计划（用大白话说）

### 第1步：测试混合调料有没有用
**目标**：看看新旧调料混合是不是比只用新调料好

**怎么做**：
1. 先跑一次程序，记录结果（比如准确率85%）
2. 把第253行改成：
   ```python
   #h=h1        # 注释掉这行
   h=h+h1       # 去掉这行的注释
   ```
3. 再跑一次程序，记录结果（比如准确率87%）
4. 对比：如果87% > 85%，说明混合调料更好

### 第2步：调整调料用量
**目标**：找到最佳的d_model、nhead、num_layers

选择第1步中更好的方式，然后一个一个调参数：

#### 2.1 调整模型大小（d_model）
试试不同的"主菜分量"：

| 实验 | 改成什么 | 说明 |
|------|----------|------|
| 小分量 | d_model=256, nhead=8 | 训练快，但可能不够强 |
| 中分量 | d_model=512, nhead=8 | 当前用的 |
| 大分量 | d_model=768, nhead=12 | 更强，但训练慢 |

**注意**：d_model必须能被nhead整除！

#### 2.2 调整注意力头数（nhead）
试试不同的"配菜种类"：

| 实验 | 改成什么 | 说明 |
|------|----------|------|
| 少配菜 | nhead=4 | 简单 |
| 中配菜 | nhead=8 | 当前用的 |
| 多配菜 | nhead=16 | 复杂 |

#### 2.3 调整层数（num_layers）
试试不同的"烹饪步骤"：

| 实验 | 改成什么 | 说明 |
|------|----------|------|
| 简单做法 | num_layers=1 | 快但可能不够好 |
| 当前做法 | num_layers=2 | 现在用的 |
| 复杂做法 | num_layers=3 | 慢但可能更好 |

### 第3步：精细调味
在最佳配置基础上，调整混合比例：

| 实验 | 改成什么 | 说明 |
|------|----------|------|
| 偏向原味 | h=0.7*h+0.3*h1 | 保留更多原来的特征 |
| 平衡 | h=0.5*h+0.5*h1 | 新旧各一半 |
| 偏向新味 | h=0.3*h+0.7*h1 | 更依赖新特征 |

## 具体操作步骤

### 怎么改代码？

#### 改第228行的参数：
```python
# 原来的
self.transformer_encoder= nn.TransformerEncoder(nn.TransformerEncoderLayer(d_model=512, nhead=8).to(device="cuda:0"), num_layers=2).to(device="cuda:0")

# 比如改成小模型
self.transformer_encoder= nn.TransformerEncoder(nn.TransformerEncoderLayer(d_model=256, nhead=8).to(device="cuda:0"), num_layers=2).to(device="cuda:0")

# 比如改成大模型
self.transformer_encoder= nn.TransformerEncoder(nn.TransformerEncoderLayer(d_model=768, nhead=12).to(device="cuda:0"), num_layers=3).to(device="cuda:0")
```

#### 改第250-253行的残差连接：
```python
# 原来的（无残差）
h1 = self.transformer_encoder(h.to(device="cuda:0"))
h=h1
#h=h1+h

# 改成有残差
h1 = self.transformer_encoder(h.to(device="cuda:0"))
#h=h1
h=h+h1

# 改成加权残差
h1 = self.transformer_encoder(h.to(device="cuda:0"))
#h=h1
h=0.3*h+0.7*h1
```

### 怎么记录结果？

建议用Excel表格记录：

| 实验编号 | d_model | nhead | num_layers | 残差方式 | 准确率 | 训练时间 | 备注 |
|----------|---------|-------|------------|----------|--------|----------|------|
| 1 | 512 | 8 | 2 | 无残差 | 85% | 30分钟 | 原始配置 |
| 2 | 512 | 8 | 2 | h+h1 | 87% | 32分钟 | 加了残差 |
| 3 | 256 | 8 | 2 | h+h1 | 84% | 20分钟 | 小模型 |
| ... | ... | ... | ... | ... | ... | ... | ... |

## 实验建议（重要！）

### 1. 一次只改一个参数
❌ **错误做法**：同时改d_model=256, nhead=4, num_layers=3
✅ **正确做法**：先只改d_model=256，其他保持不变

**为什么？** 这样你才知道是哪个参数的改变导致了结果变化。

### 2. 实验顺序很重要
1. **先测残差**：确定要不要混合调料
2. **再调参数**：一个一个调d_model、nhead、num_layers
3. **最后精调**：调整混合比例

### 3. 什么时候停止实验？
- 如果某个配置明显很差（准确率下降很多），可以跳过
- 如果训练时间太长（比如超过2小时），可以考虑用小一点的参数
- 找到明显最好的配置后，可以停止

### 4. 常见问题
**Q：改了参数后程序报错怎么办？**
A：检查d_model能不能被nhead整除。比如d_model=512, nhead=6就会报错，因为512÷6不是整数。

**Q：训练太慢怎么办？**
A：先用小参数（d_model=256, num_layers=1）快速验证，确定有效果再用大参数。

**Q：结果差不多怎么选？**
A：选训练时间短的，或者参数少的（更简单的模型通常更好）。

## 预期结果

根据经验，你可能会发现：
- **加残差连接**通常会提升性能
- **d_model=512**是个不错的起点
- **num_layers=2或3**通常够用
- **太大的参数**可能导致过拟合（训练集好，测试集差）

## 总结

这个实验就像调菜谱一样：
1. 先确定要不要混合调料（残差连接）
2. 再调整各种调料用量（Transformer参数）
3. 最后精细调味（残差权重）

记住：**一次只改一个参数，详细记录结果，耐心对比**。

通过这样的实验，你的模型性能应该会有明显提升！有问题随时问我。
