# SlotGAT Project Requirements
# Compatible versions for CUDA 11.7 and Python 3.8-3.11

# Core dependencies
numpy==1.24.3

# PyTorch ecosystem (CUDA 11.7)
--extra-index-url https://download.pytorch.org/whl/cu117
torch==1.13.1+cu117
torchvision==0.14.1+cu117
torchaudio==0.13.1+cu117

# DGL (Deep Graph Library)
--find-links https://data.dgl.ai/wheels/cu117/repo.html
dgl==1.0.1+cu117

# PyTorch Geometric and dependencies
--find-links https://data.pyg.org/whl/torch-1.13.0+cu117.html
torch-sparse==0.6.16
torch-scatter==2.1.0
torch-cluster==1.6.0
torch-geometric==2.2.0

# Scientific computing
networkx==2.8.4
scikit-learn==1.2.1
scipy==1.10.0

# Optional: Additional utilities
matplotlib>=3.5.0
pandas>=1.4.0
tqdm>=4.64.0
