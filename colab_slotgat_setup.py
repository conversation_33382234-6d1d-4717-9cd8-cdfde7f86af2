#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SlotGAT Google Colab 完整环境配置脚本
适用于 ICML 2023 SlotGAT 项目
"""

import os
import sys
import subprocess
import time

def print_header(text):
    """打印格式化的标题"""
    print("\n" + "="*60)
    print(f"🚀 {text}")
    print("="*60)

def print_step(step_num, text):
    """打印步骤信息"""
    print(f"\n📋 步骤 {step_num}: {text}")
    print("-" * 40)

def run_command(cmd, description=""):
    """执行命令并显示结果"""
    if description:
        print(f"🔧 {description}")
    print(f"执行: {cmd}")
    
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        print("✅ 成功")
        return True
    else:
        print(f"❌ 失败: {result.stderr}")
        return False

def check_colab_environment():
    """检查是否在Colab环境中"""
    try:
        import google.colab
        print("✅ 检测到Google Colab环境")
        return True
    except ImportError:
        print("❌ 未检测到Google Colab环境")
        return False

def mount_google_drive():
    """挂载Google Drive"""
    try:
        from google.colab import drive
        drive.mount('/content/drive')
        print("✅ Google Drive 挂载成功")
        return True
    except Exception as e:
        print(f"❌ Google Drive 挂载失败: {e}")
        return False

def setup_project_directory():
    """设置项目目录"""
    print("🔍 查找项目目录...")
    
    # 可能的项目路径
    possible_paths = [
        '/content/drive/MyDrive/SlotGAT_ICML23-new',
        '/content/drive/MyDrive/SlotGAT_Project',
        '/content/SlotGAT_ICML23-new',
        '/content'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ 找到项目目录: {path}")
            os.chdir(path)
            return path
    
    # 如果没找到，创建工作目录
    work_dir = '/content/SlotGAT_workspace'
    os.makedirs(work_dir, exist_ok=True)
    os.chdir(work_dir)
    print(f"📁 创建工作目录: {work_dir}")
    return work_dir

def install_dependencies():
    """安装项目依赖"""
    print_step(1, "清理现有环境")
    run_command(
        "pip uninstall -y torch torchvision torchaudio dgl torch-geometric torch-sparse torch-scatter torch-cluster",
        "卸载可能冲突的包"
    )
    
    print_step(2, "安装NumPy")
    run_command("pip install numpy==1.24.3", "安装兼容的NumPy版本")
    
    print_step(3, "安装PyTorch生态系统")
    # 安装PyTorch 1.13.1 + CUDA 11.7
    run_command(
        "pip install torch==1.13.1+cu117 torchvision==0.14.1+cu117 torchaudio==0.13.1+cu117 --extra-index-url https://download.pytorch.org/whl/cu117",
        "安装PyTorch 1.13.1 with CUDA 11.7"
    )
    
    print_step(4, "安装DGL")
    run_command(
        "pip install dgl==1.0.1+cu117 -f https://data.dgl.ai/wheels/cu117/repo.html",
        "安装DGL 1.0.1"
    )
    
    print_step(5, "安装PyTorch Geometric依赖")
    # 分步安装PyG依赖
    pyg_deps = [
        "torch-sparse==0.6.16",
        "torch-scatter==2.1.0", 
        "torch-cluster==1.6.0"
    ]
    
    for dep in pyg_deps:
        run_command(
            f"pip install {dep} -f https://data.pyg.org/whl/torch-1.13.0+cu117.html",
            f"安装 {dep}"
        )
    
    print_step(6, "安装PyTorch Geometric主包")
    run_command("pip install torch-geometric==2.2.0", "安装PyG 2.2.0")
    
    print_step(7, "安装其他科学计算依赖")
    other_deps = [
        "networkx==2.8.4",
        "scikit-learn==1.2.1", 
        "scipy==1.10.0"
    ]
    
    for dep in other_deps:
        run_command(f"pip install {dep}", f"安装 {dep}")

def verify_installation():
    """验证安装结果"""
    print_step(8, "验证安装")
    
    packages_to_check = [
        ("torch", "PyTorch"),
        ("dgl", "DGL"),
        ("torch_geometric", "PyTorch Geometric"),
        ("torch_sparse", "torch-sparse"),
        ("torch_scatter", "torch-scatter"),
        ("torch_cluster", "torch-cluster"),
        ("networkx", "NetworkX"),
        ("sklearn", "Scikit-learn"),
        ("scipy", "SciPy"),
        ("numpy", "NumPy")
    ]
    
    success_count = 0
    for module_name, display_name in packages_to_check:
        try:
            module = __import__(module_name)
            version = getattr(module, '__version__', 'Unknown')
            print(f"  ✅ {display_name}: {version}")
            success_count += 1
        except ImportError as e:
            print(f"  ❌ {display_name}: 导入失败")
    
    print(f"\n📊 验证结果: {success_count}/{len(packages_to_check)} 包安装成功")
    
    # 检查CUDA支持
    try:
        import torch
        if torch.cuda.is_available():
            device_name = torch.cuda.get_device_name(0)
            print(f"🚀 CUDA可用: {device_name}")
            print(f"🔥 PyTorch CUDA版本: {torch.version.cuda}")
        else:
            print("⚠️ CUDA不可用，将使用CPU模式")
    except:
        print("❌ 无法检查CUDA状态")
    
    return success_count >= 8  # 至少8个核心包成功

def download_project_data():
    """下载项目数据和代码"""
    print_step(9, "准备项目数据")
    
    print("📥 项目数据下载链接:")
    print("  • 节点分类 (NC): https://drive.google.com/drive/folders/1Ga68xitx5MMxT7XW95xIQiJFs2qgS1z1")
    print("  • 链接预测 (LP): https://drive.google.com/drive/folders/1mFAlrQwQeKLEJ3Tv8fatmHninRn6Fj68")
    print("  • 项目代码: https://github.com/scottjiao/SlotGAT_ICML23/")
    
    print("\n💡 请手动下载数据到Google Drive，然后:")
    print("  1. 将数据放在 /content/drive/MyDrive/SlotGAT_Data/ 目录下")
    print("  2. 保持与Google Drive链接中相同的目录结构")

def create_colab_notebook_cells():
    """生成Colab notebook代码单元"""
    print_step(10, "生成Colab代码单元")
    
    cells = {
        "环境设置": '''# SlotGAT 环境设置
!python colab_slotgat_setup.py''',
        
        "验证安装": '''# 验证安装
import torch
import dgl
import torch_geometric
import numpy as np

print(f"PyTorch: {torch.__version__}")
print(f"DGL: {dgl.__version__}")
print(f"PyG: {torch_geometric.__version__}")
print(f"NumPy: {np.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")''',
        
        "切换到项目目录": '''# 切换到项目目录
import os

# 节点分类任务
nc_dir = "/content/drive/MyDrive/SlotGAT_Data/NC/methods/SlotGAT"
# 链接预测任务  
lp_dir = "/content/drive/MyDrive/SlotGAT_Data/LP/methods/SlotGAT"

# 选择任务类型
task_type = "NC"  # 或 "LP"
work_dir = nc_dir if task_type == "NC" else lp_dir

if os.path.exists(work_dir):
    os.chdir(work_dir)
    print(f"✅ 切换到: {os.getcwd()}")
else:
    print(f"❌ 目录不存在: {work_dir}")
    print("请确保已下载项目数据到Google Drive")''',
        
        "训练模型": '''# 训练SlotGAT模型
!python run_train_slotGAT_on_all_dataset.py''',
        
        "评估模型": '''# 评估训练好的模型
!python run_use_slotGAT_on_all_dataset.py'''
    }
    
    print("📝 Colab Notebook 代码单元已生成:")
    for title, code in cells.items():
        print(f"\n--- {title} ---")
        print(code)
        print()

def main():
    """主函数"""
    print_header("SlotGAT Google Colab 环境配置")
    
    # 检查Colab环境
    if not check_colab_environment():
        print("⚠️ 此脚本专为Google Colab设计")
        return
    
    # 挂载Google Drive
    mount_google_drive()
    
    # 设置项目目录
    project_dir = setup_project_directory()
    
    # 安装依赖
    install_dependencies()
    
    # 验证安装
    if verify_installation():
        print("\n🎉 环境配置成功!")
    else:
        print("\n⚠️ 部分依赖安装可能有问题，但可以尝试运行")
    
    # 下载数据提示
    download_project_data()
    
    # 生成代码单元
    create_colab_notebook_cells()
    
    print_header("配置完成")
    print("🚀 接下来的步骤:")
    print("  1. 重启Colab运行时 (Runtime -> Restart Runtime)")
    print("  2. 下载项目数据到Google Drive")
    print("  3. 使用上面生成的代码单元运行实验")
    print("  4. 如有问题，请检查CUDA版本和依赖兼容性")

if __name__ == "__main__":
    main()
