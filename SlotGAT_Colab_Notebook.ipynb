{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# SlotGAT ICML 2023 - Google Colab 运行环境\n", "\n", "这个notebook用于在Google Colab上运行SlotGAT项目。\n", "\n", "**重要提示:**\n", "1. 确保启用GPU运行时 (Runtime -> Change runtime type -> GPU)\n", "2. 按顺序执行所有代码单元\n", "3. 第一次运行需要重启运行时"]}, {"cell_type": "markdown", "metadata": {"id": "setup-header"}, "source": ["## 1. 环境设置和依赖安装"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install-deps"}, "outputs": [], "source": ["# SlotGAT 依赖安装\n", "import os\n", "import sys\n", "\n", "print(\"🚀 开始安装SlotGAT依赖...\")\n", "print(f\"Python版本: {sys.version}\")\n", "\n", "# 1. 清理环境\n", "print(\"\\n🧹 清理现有环境...\")\n", "!pip uninstall -y torch torchvision torchaudio dgl torch-geometric torch-sparse torch-scatter torch-cluster\n", "\n", "# 2. 安装<PERSON><PERSON><PERSON><PERSON>\n", "print(\"\\n📦 安装NumPy...\")\n", "!pip install numpy==1.24.3\n", "\n", "# 3. 安装<PERSON><PERSON><PERSON><PERSON><PERSON>\n", "print(\"\\n🔥 安装PyTorch 1.13.1...\")\n", "!pip install torch==1.13.1+cu117 torchvision==0.14.1+cu117 torchaudio==0.13.1+cu117 --extra-index-url https://download.pytorch.org/whl/cu117\n", "\n", "# 4. 安装DGL\n", "print(\"\\n🌐 安装DGL...\")\n", "!pip install dgl==1.0.1+cu117 -f https://data.dgl.ai/wheels/cu117/repo.html\n", "\n", "# 5. 安装PyG依赖\n", "print(\"\\n📊 安装PyTorch Geometric依赖...\")\n", "!pip install torch-sparse==0.6.16 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html\n", "!pip install torch-scatter==2.1.0 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html\n", "!pip install torch-cluster==1.6.0 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html\n", "\n", "# 6. 安装PyG主包\n", "print(\"\\n📈 安装PyTorch Geometric...\")\n", "!pip install torch-geometric==2.2.0\n", "\n", "# 7. 安装其他依赖\n", "print(\"\\n🔬 安装其他依赖...\")\n", "!pip install networkx==2.8.4 scikit-learn==1.2.1 scipy==1.10.0\n", "\n", "print(\"\\n✅ 依赖安装完成！\")\n", "print(\"⚠️ 请重启运行时后继续 (Runtime -> Restart Runtime)\")"]}, {"cell_type": "markdown", "metadata": {"id": "verify-header"}, "source": ["## 2. 验证安装 (重启运行时后执行)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "verify-install"}, "outputs": [], "source": ["# 验证安装\n", "import torch\n", "import dgl\n", "import torch_geometric\n", "import numpy as np\n", "import networkx as nx\n", "import sklearn\n", "import scipy\n", "\n", "print(\"📋 安装验证结果:\")\n", "print(f\"  ✅ PyTorch: {torch.__version__}\")\n", "print(f\"  ✅ DGL: {dgl.__version__}\")\n", "print(f\"  ✅ PyTorch Geometric: {torch_geometric.__version__}\")\n", "print(f\"  ✅ NumPy: {np.__version__}\")\n", "print(f\"  ✅ NetworkX: {nx.__version__}\")\n", "print(f\"  ✅ Scikit-learn: {sklearn.__version__}\")\n", "print(f\"  ✅ SciPy: {scipy.__version__}\")\n", "\n", "print(f\"\\n🚀 CUDA可用: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"🔥 GPU设备: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"💾 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB\")\n", "\n", "# 测试核心功能\n", "try:\n", "    import torch_sparse\n", "    import torch_scatter\n", "    import torch_cluster\n", "    print(\"\\n✅ PyTorch Geometric扩展包导入成功\")\n", "except ImportError as e:\n", "    print(f\"\\n❌ PyTorch Geometric扩展包导入失败: {e}\")\n", "\n", "print(\"\\n🎉 环境验证完成！\")"]}, {"cell_type": "markdown", "metadata": {"id": "drive-header"}, "source": ["## 3. 挂载Google Drive并设置项目"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount-drive"}, "outputs": [], "source": ["# 挂载Google Drive\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "print(\"✅ Google Drive 挂载成功\")\n", "print(\"\\n📥 请确保已将以下数据下载到Google Drive:\")\n", "print(\"  • 节点分类数据: https://drive.google.com/drive/folders/1Ga68xitx5MMxT7XW95xIQiJFs2qgS1z1\")\n", "print(\"  • 链接预测数据: https://drive.google.com/drive/folders/1mFAlrQwQeKLEJ3Tv8fatmHninRn6Fj68\")\n", "print(\"  • 项目代码: https://github.com/scottjiao/SlotGAT_ICML23/\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup-project"}, "outputs": [], "source": ["# 设置项目目录\n", "import os\n", "\n", "# 定义可能的项目路径\n", "possible_paths = [\n", "    '/content/drive/MyDrive/SlotGAT_ICML23-new',\n", "    '/content/drive/MyDrive/SlotGAT_Project', \n", "    '/content/drive/MyDrive/SlotGAT'\n", "]\n", "\n", "project_root = None\n", "for path in possible_paths:\n", "    if os.path.exists(path):\n", "        project_root = path\n", "        print(f\"✅ 找到项目根目录: {path}\")\n", "        break\n", "\n", "if project_root is None:\n", "    print(\"❌ 未找到项目目录，请检查Google Drive中的文件\")\n", "    print(\"💡 请确保项目文件已上传到Google Drive\")\n", "else:\n", "    # 显示项目结构\n", "    print(f\"\\n📁 项目结构:\")\n", "    for root, dirs, files in os.walk(project_root):\n", "        level = root.replace(project_root, '').count(os.sep)\n", "        indent = ' ' * 2 * level\n", "        print(f\"{indent}{os.path.basename(root)}/\")\n", "        subindent = ' ' * 2 * (level + 1)\n", "        for file in files[:5]:  # 只显示前5个文件\n", "            print(f\"{subindent}{file}\")\n", "        if len(files) > 5:\n", "            print(f\"{subindent}... 还有 {len(files)-5} 个文件\")\n", "        if level > 2:  # 限制显示深度\n", "            break"]}, {"cell_type": "markdown", "metadata": {"id": "task-header"}, "source": ["## 4. 选择任务并切换目录"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "choose-task"}, "outputs": [], "source": ["# 选择任务类型\n", "TASK_TYPE = \"NC\"  # 改为 \"LP\" 进行链接预测任务\n", "\n", "if project_root:\n", "    if TASK_TYPE == \"NC\":\n", "        work_dir = os.path.join(project_root, \"NC\", \"methods\", \"SlotGAT\")\n", "        print(\"🎯 选择任务: 节点分类 (Node Classification)\")\n", "    elif <PERSON>_TYPE == \"LP\":\n", "        work_dir = os.path.join(project_root, \"LP\", \"methods\", \"SlotGAT\")\n", "        print(\"🎯 选择任务: 链接预测 (Link Prediction)\")\n", "    else:\n", "        print(\"❌ 无效的任务类型，请设置 TASK_TYPE 为 'NC' 或 'LP'\")\n", "        work_dir = None\n", "    \n", "    if work_dir and os.path.exists(work_dir):\n", "        os.ch<PERSON>(work_dir)\n", "        print(f\"✅ 切换到工作目录: {os.getcwd()}\")\n", "        \n", "        # 检查关键文件\n", "        key_files = [\n", "            'run_train_slotGAT_on_all_dataset.py',\n", "            'run_use_slotGAT_on_all_dataset.py',\n", "            'GNN_bak.py'\n", "        ]\n", "        \n", "        print(\"\\n📋 检查关键文件:\")\n", "        for file in key_files:\n", "            if os.path.exists(file):\n", "                print(f\"  ✅ {file}\")\n", "            else:\n", "                print(f\"  ❌ {file} 缺失\")\n", "        \n", "        # 创建必要目录\n", "        dirs_to_create = ['outputs', 'log', 'checkpoint']\n", "        for dir_name in dirs_to_create:\n", "            if not os.path.exists(dir_name):\n", "                os.makedirs(dir_name)\n", "                print(f\"📁 创建目录: {dir_name}\")\n", "    else:\n", "        print(f\"❌ 工作目录不存在: {work_dir}\")\n", "else:\n", "    print(\"❌ 请先设置项目根目录\")"]}, {"cell_type": "markdown", "metadata": {"id": "train-header"}, "source": ["## 5. 训练模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "train-model"}, "outputs": [], "source": ["# 训练SlotGAT模型\n", "print(\"🚀 开始训练SlotGAT模型...\")\n", "print(f\"当前目录: {os.getcwd()}\")\n", "\n", "# 检查训练脚本是否存在\n", "if os.path.exists('run_train_slotGAT_on_all_dataset.py'):\n", "    !python run_train_slotGAT_on_all_dataset.py\n", "else:\n", "    print(\"❌ 训练脚本不存在，请检查项目文件\")"]}, {"cell_type": "markdown", "metadata": {"id": "eval-header"}, "source": ["## 6. 评估模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "eval-model"}, "outputs": [], "source": ["# 评估训练好的模型\n", "print(\"📊 开始评估SlotGAT模型...\")\n", "\n", "# 检查评估脚本是否存在\n", "if os.path.exists('run_use_slotGAT_on_all_dataset.py'):\n", "    !python run_use_slotGAT_on_all_dataset.py\n", "else:\n", "    print(\"❌ 评估脚本不存在，请检查项目文件\")"]}, {"cell_type": "markdown", "metadata": {"id": "results-header"}, "source": ["## 7. 查看结果"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "view-results"}, "outputs": [], "source": ["# 查看实验结果\n", "import glob\n", "\n", "print(\"📋 实验结果:\")\n", "\n", "# 查看日志文件\n", "log_files = glob.glob('log/*.log')\n", "if log_files:\n", "    print(f\"\\n📄 找到 {len(log_files)} 个日志文件:\")\n", "    for log_file in log_files[-3:]:  # 显示最新的3个\n", "        print(f\"  📝 {log_file}\")\n", "        \n", "    # 显示最新日志的最后几行\n", "    latest_log = max(log_files, key=os.path.getctime)\n", "    print(f\"\\n📖 最新日志 ({latest_log}) 的最后10行:\")\n", "    !tail -10 \"{latest_log}\"\n", "else:\n", "    print(\"❌ 未找到日志文件\")\n", "\n", "# 查看输出目录\n", "if os.path.exists('outputs'):\n", "    output_files = os.listdir('outputs')\n", "    print(f\"\\n📁 输出目录包含 {len(output_files)} 个文件\")\n", "    for file in output_files[:5]:\n", "        print(f\"  📄 {file}\")\n", "\n", "# 查看检查点\n", "if os.path.exists('checkpoint'):\n", "    checkpoint_files = os.listdir('checkpoint')\n", "    print(f\"\\n💾 检查点目录包含 {len(checkpoint_files)} 个文件\")\n", "    for file in checkpoint_files[:5]:\n", "        print(f\"  🔄 {file}\")"]}]}