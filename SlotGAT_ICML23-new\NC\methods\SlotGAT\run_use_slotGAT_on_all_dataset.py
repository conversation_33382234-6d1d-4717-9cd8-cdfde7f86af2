import time
import subprocess
import multiprocessing
from threading import main_thread
from pipeline_utils import config_study_name,Run
import os 

if not os.path.exists("outputs"):
    os.mkdir("outputs")
if not os.path.exists("log"):
    os.mkdir("log")
if not os.path.exists("checkpoint"):
    os.mkdir("checkpoint")

# 修改为只使用一个GPU
resources_dict={"0":1}   #id:load

start_time=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

fixed_info_by_selected_keys={
    "slotGAT":{"task_property":"get_results_use_trained","net":"slotGAT","slot_aggregator":"SA","inProcessEmb":"True",
                        "use_trained":"True",
                        "trained_dir":"outputs",
                        "save_trained":"False",
                        } }

# 数据集配置
datasets = [
    ("ACM", {
        "search_hidden_dim":"[64]",
        "search_num_layers":"[2]",
        "search_lr":"[0.001]",
        "search_weight_decay":"[0.0001]",
        "feats-type":1,
        "num-heads":8,
        "epoch":300,
        "SAattDim":32,
        "dropout_feat":0.8,
        "dropout_attn":0.8
    }),
    ("DBLP", {
        "search_hidden_dim":"[64]",
        "search_num_layers":"[4]",
        "search_lr":"[0.0001]",
        "search_weight_decay":"[0.001]",
        "feats-type":1,
        "num-heads":8,
        "epoch":300,
        "SAattDim":32,
        "dropout_feat":0.5,
        "dropout_attn":0.5
    }),
    ("IMDB", {
        "search_hidden_dim":"[128]",
        "search_num_layers":"[3]",
        "search_lr":"[0.0001]",
        "search_weight_decay":"[0.001]",
        "feats-type":1,
        "num-heads":8,
        "epoch":300,
        "SAattDim":3,
        "dropout_feat":0.8,
        "dropout_attn":0.2
    }),
    ("Freebase", {
        "search_hidden_dim":"[16]",
        "search_num_layers":"[2]",
        "search_lr":"[0.0005]",
        "search_weight_decay":"[0.001]",
        "feats-type":2,
        "num-heads":8,
        "epoch":300,
        "SAattDim":8,
        "dropout_feat":0.5,
        "dropout_attn":0.5,
        "edge-feats":0
    }),
    ("PubMed_NC", {
        "search_hidden_dim":"[128]",
        "search_num_layers":"[2]",
        "search_lr":"[0.005]",
        "search_weight_decay":"[0.001]",
        "feats-type":1,
        "num-heads":8,
        "epoch":300,
        "SAattDim":3,
        "dropout_feat":0.2,
        "dropout_attn":0.8
    })
]

if __name__ == '__main__':
    # 串行运行每个数据集
    for dataset, params in datasets:
        print(f"\n{'='*50}")
        print(f"Running {dataset} dataset")
        print(f"{'='*50}\n")
        
        # 构建命令
        cmd = f"python run_analysis.py"
        for key, value in params.items():
            cmd += f" --{key} {value}"
        
        # 添加固定参数
        cmd += f" --task_property get_results_use_trained"
        cmd += f" --net slotGAT"
        cmd += f" --slot_aggregator SA"
        cmd += f" --verbose True"
        cmd += f" --use_trained True"
        cmd += f" --trained_dir outputs"
        cmd += f" --save_trained False"
        cmd += f" --dataset {dataset}"
        cmd += f" --repeat 5"
        cmd += f" --study_name get_results_use_trained_dataset_{dataset}_net_slotGAT"
        cmd += f" --gpu 0"
        
        # 运行命令
        print(f"Running command:\n{cmd}\n")
        subprocess.run(cmd, shell=True)
        
        print(f"\nFinished {dataset} dataset\n")