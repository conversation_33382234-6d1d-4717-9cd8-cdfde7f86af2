#!/usr/bin/env python3
# ==================== SlotGAT Conda环境配置脚本 ====================
"""
使用Conda管理依赖的SlotGAT环境配置脚本
推荐在有conda的环境中使用此脚本
"""

import os
import sys
import subprocess
import platform

def run_command(cmd, shell=True):
    """运行命令并显示输出"""
    print(f"执行: {cmd}")
    try:
        result = subprocess.run(cmd, shell=shell, capture_output=True, text=True, timeout=300)
        if result.returncode != 0:
            print(f"❌ 错误: {result.stderr}")
            return False
        else:
            print("✅ 成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
            return True
    except subprocess.TimeoutExpired:
        print("❌ 命令执行超时")
        return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

def check_conda():
    """检查conda是否可用"""
    try:
        result = subprocess.run(['conda', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ 检测到Conda: {result.stdout.strip()}")
            return True
        else:
            return False
    except FileNotFoundError:
        return False

def main():
    print("🚀 开始配置 SlotGAT Conda环境...")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {sys.version}")
    
    # 检查conda
    if not check_conda():
        print("❌ 未检测到Conda，请先安装Anaconda或Miniconda")
        print("下载地址: https://docs.conda.io/en/latest/miniconda.html")
        return False
    
    # 创建conda环境
    env_name = "slotgat"
    print(f"\n📦 创建Conda环境: {env_name}")
    
    # 删除已存在的环境
    run_command(f"conda env remove -n {env_name} -y")
    
    # 创建新环境
    if not run_command(f"conda create -n {env_name} python=3.10 -y"):
        print("❌ 环境创建失败")
        return False
    
    print(f"\n🔧 在环境 {env_name} 中安装依赖...")
    
    # 激活环境的命令前缀
    if platform.system() == "Windows":
        conda_activate = f"conda activate {env_name} && "
    else:
        conda_activate = f"source activate {env_name} && "
    
    # 安装基础包
    packages = [
        # 基础科学计算
        "numpy=1.24.3",
        "scipy=1.10.0",
        "networkx=2.8.4",
        "scikit-learn=1.2.1",
        "matplotlib",
        "pandas",
        "tqdm",
        
        # CUDA工具包
        "cudatoolkit=11.7",
    ]
    
    for package in packages:
        if not run_command(f"{conda_activate}conda install {package} -y"):
            print(f"⚠️ {package} 安装失败，继续...")
    
    # 安装PyTorch
    print("\n🔥 安装PyTorch...")
    pytorch_cmd = f"{conda_activate}conda install pytorch=1.13.1 torchvision=0.14.1 torchaudio=0.13.1 pytorch-cuda=11.7 -c pytorch -c nvidia -y"
    if not run_command(pytorch_cmd):
        print("❌ PyTorch安装失败")
        return False
    
    # 安装DGL
    print("\n🌐 安装DGL...")
    dgl_cmd = f"{conda_activate}pip install dgl==1.0.1+cu117 -f https://data.dgl.ai/wheels/cu117/repo.html"
    if not run_command(dgl_cmd):
        print("❌ DGL安装失败")
        return False
    
    # 安装PyTorch Geometric
    print("\n📊 安装PyTorch Geometric...")
    pyg_commands = [
        f"{conda_activate}pip install torch-sparse==0.6.16 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html",
        f"{conda_activate}pip install torch-scatter==2.1.0 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html",
        f"{conda_activate}pip install torch-cluster==1.6.0 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html",
        f"{conda_activate}pip install torch-geometric==2.2.0"
    ]
    
    for cmd in pyg_commands:
        if not run_command(cmd):
            print("⚠️ PyG组件安装失败，继续...")
    
    # 验证安装
    print("\n🔍 验证安装...")
    verification_script = f"""
import sys
print(f"Python: {{sys.version}}")

try:
    import torch
    print(f"PyTorch: {{torch.__version__}}")
    print(f"CUDA available: {{torch.cuda.is_available()}}")
    if torch.cuda.is_available():
        print(f"CUDA device: {{torch.cuda.get_device_name(0)}}")
except ImportError as e:
    print(f"PyTorch导入失败: {{e}}")

try:
    import dgl
    print(f"DGL: {{dgl.__version__}}")
except ImportError as e:
    print(f"DGL导入失败: {{e}}")

try:
    import torch_geometric
    print(f"PyG: {{torch_geometric.__version__}}")
except ImportError as e:
    print(f"PyG导入失败: {{e}}")

try:
    import numpy as np
    import scipy
    import sklearn
    import networkx as nx
    print(f"NumPy: {{np.__version__}}")
    print(f"SciPy: {{scipy.__version__}}")
    print(f"Scikit-learn: {{sklearn.__version__}}")
    print(f"NetworkX: {{nx.__version__}}")
except ImportError as e:
    print(f"科学计算包导入失败: {{e}}")
"""
    
    # 将验证脚本写入临时文件
    with open("verify_install.py", "w") as f:
        f.write(verification_script)
    
    # 运行验证
    verify_cmd = f"{conda_activate}python verify_install.py"
    run_command(verify_cmd)
    
    # 清理临时文件
    try:
        os.remove("verify_install.py")
    except:
        pass
    
    print("\n" + "="*60)
    print("🎉 Conda环境配置完成!")
    print("="*60)
    print(f"环境名称: {env_name}")
    print("\n🚀 使用方法:")
    if platform.system() == "Windows":
        print(f"  激活环境: conda activate {env_name}")
    else:
        print(f"  激活环境: source activate {env_name}")
    print("  运行训练: python run_train_slotGAT_on_all_dataset.py")
    print("  运行评估: python run_use_slotGAT_on_all_dataset.py")
    print(f"  停用环境: conda deactivate")
    
    print("\n💡 注意事项:")
    print("  1. 每次使用前需要激活环境")
    print("  2. 确保在正确的项目目录中运行")
    print("  3. 如有问题，可以删除环境重新创建:")
    print(f"     conda env remove -n {env_name}")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 环境配置失败，请检查错误信息")
        sys.exit(1)
