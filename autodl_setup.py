# ==================== SlotGAT AutoDL 环境配置脚本 ====================
print("🚀 开始在AutoDL环境中配置 SlotGAT...")

import os
import sys
import subprocess
import platform

def run_command(cmd):
    """运行命令并显示输出"""
    print(f"执行: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"❌ 错误: {result.stderr}")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return False
    else:
        print("✅ 成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True

def check_environment():
    """检查AutoDL环境信息"""
    print("🔍 检查AutoDL环境信息...")
    
    # 检查Python版本
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    # 检查操作系统
    print(f"操作系统: {platform.system()} {platform.release()}")
    
    # 检查CUDA版本
    try:
        result = subprocess.run("nvidia-smi", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ NVIDIA GPU 可用")
            # 提取CUDA版本信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'CUDA Version' in line:
                    print(f"CUDA版本: {line.split('CUDA Version: ')[1].split(' ')[0]}")
                    break
        else:
            print("⚠️ 未检测到NVIDIA GPU")
    except:
        print("⚠️ 无法检查GPU状态")
    
    # 检查当前已安装的包
    print("\n📦 检查当前已安装的关键包...")
    key_packages = ['torch', 'dgl', 'torch_geometric', 'numpy', 'scipy']
    for pkg in key_packages:
        try:
            module = __import__(pkg)
            version = getattr(module, '__version__', 'Unknown')
            print(f"  ✅ {pkg}: {version}")
        except ImportError:
            print(f"  ❌ {pkg}: 未安装")

def install_dependencies():
    """安装SlotGAT依赖"""
    print("\n📦 开始安装依赖包...")
    
    # 1. 更新pip
    print("更新pip...")
    run_command("pip install --upgrade pip")
    
    # 2. 安装基础科学计算包
    print("\n安装基础科学计算包...")
    run_command("pip install numpy==1.24.3 scipy==1.10.0 networkx==2.8.4 scikit-learn==1.2.1")
    
    # 3. 检查CUDA版本并安装对应的PyTorch
    print("\n安装PyTorch...")
    # AutoDL通常预装了CUDA 11.7或11.8，我们使用11.7版本以确保兼容性
    run_command("pip install torch==1.13.1+cu117 torchvision==0.14.1+cu117 torchaudio==0.13.1+cu117 --extra-index-url https://download.pytorch.org/whl/cu117")
    
    # 4. 安装DGL
    print("\n安装DGL...")
    run_command("pip install dgl==1.0.1+cu117 -f https://data.dgl.ai/wheels/cu117/repo.html")
    
    # 5. 安装PyTorch Geometric依赖
    print("\n安装PyTorch Geometric依赖...")
    run_command("pip install torch-sparse==0.6.16 torch-scatter==2.1.0 torch-cluster==1.6.0 -f https://data.pyg.org/whl/torch-1.13.0+cu117.html")
    
    # 6. 安装PyTorch Geometric主包
    print("\n安装PyTorch Geometric...")
    run_command("pip install torch-geometric==2.2.0")

def verify_installation():
    """验证安装结果"""
    print("\n🔍 验证安装结果...")
    
    packages_to_verify = [
        ("NumPy", "numpy"),
        ("SciPy", "scipy"),
        ("NetworkX", "networkx"),
        ("Scikit-learn", "sklearn"),
        ("PyTorch", "torch"),
        ("DGL", "dgl"),
        ("PyTorch Geometric", "torch_geometric"),
        ("torch-sparse", "torch_sparse"),
        ("torch-scatter", "torch_scatter"),
        ("torch-cluster", "torch_cluster")
    ]
    
    success_count = 0
    total_packages = len(packages_to_verify)
    
    for package_name, import_name in packages_to_verify:
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', 'Unknown')
            print(f"  ✅ {package_name}: {version}")
            success_count += 1
        except ImportError as e:
            print(f"  ❌ {package_name}: 导入失败 - {e}")
    
    print(f"\n📊 安装结果: {success_count}/{total_packages} 包成功安装")
    
    # 测试CUDA支持
    try:
        import torch
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0)
            print(f"🚀 CUDA可用: {device_count} GPU(s), 主GPU: {device_name}")
            print(f"   PyTorch CUDA版本: {torch.version.cuda}")
        else:
            print("⚠️ CUDA不可用，将使用CPU模式")
    except Exception as e:
        print(f"⚠️ CUDA检查失败: {e}")
    
    return success_count >= 8  # 至少8个核心包安装成功

def setup_project_structure():
    """设置项目结构"""
    print("\n📁 设置项目结构...")
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 查找SlotGAT项目目录
    slotgat_dirs = []
    for root, dirs, files in os.walk('.'):
        if 'run_train_slotGAT_on_all_dataset.py' in files:
            slotgat_dirs.append(root)
    
    if slotgat_dirs:
        print("找到SlotGAT项目目录:")
        for i, dir_path in enumerate(slotgat_dirs):
            print(f"  {i+1}. {dir_path}")
        
        # 使用第一个找到的目录
        project_dir = slotgat_dirs[0]
        print(f"使用项目目录: {project_dir}")
        
        # 创建必要的子目录
        subdirs = ['outputs', 'log', 'checkpoint', 'analysis']
        for subdir in subdirs:
            full_path = os.path.join(project_dir, subdir)
            if not os.path.exists(full_path):
                os.makedirs(full_path)
                print(f"  ✅ 创建目录: {full_path}")
            else:
                print(f"  📁 目录已存在: {full_path}")
        
        return project_dir
    else:
        print("❌ 未找到SlotGAT项目目录")
        print("请确保在包含SlotGAT项目的目录中运行此脚本")
        return None

def main():
    """主函数"""
    print("="*60)
    print("🎯 SlotGAT AutoDL 环境配置")
    print("="*60)
    
    # 1. 检查环境
    check_environment()
    
    # 2. 安装依赖
    install_dependencies()
    
    # 3. 验证安装
    if verify_installation():
        print("\n🎉 依赖安装成功!")
    else:
        print("\n⚠️ 部分依赖安装可能有问题，但可以尝试运行")
    
    # 4. 设置项目结构
    project_dir = setup_project_structure()
    
    # 5. 最终报告
    print("\n" + "="*60)
    print("🎉 AutoDL环境配置完成!")
    print("="*60)
    
    if project_dir:
        print(f"\n📂 项目目录: {project_dir}")
        print("\n🚀 接下来可以运行:")
        print(f"  cd {project_dir}")
        print("  python run_train_slotGAT_on_all_dataset.py  # 训练模型")
        print("  python run_use_slotGAT_on_all_dataset.py   # 评估模型")
    
    print("\n💡 AutoDL使用提示:")
    print("  1. 确保选择了GPU实例")
    print("  2. 如果遇到内存不足，可以调整batch_size")
    print("  3. 使用 nvidia-smi 监控GPU使用情况")
    print("  4. 定期保存训练结果到持久化存储")

if __name__ == "__main__":
    main()
